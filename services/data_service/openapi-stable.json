{"openapi": "3.1.0", "info": {"title": "Data Service (Stable)", "version": "v3"}, "paths": {"/health": {"get": {"summary": "Endpoint", "operationId": "endpoint_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/v3/asset/by_id/": {"get": {"tags": ["assets"], "summary": "Fetch Asset by ID", "description": "Retrieve and stream an asset file by its unique identifier.\n\n    This endpoint fetches an asset (such as an image, document, audio, or video file) from the user's\n    storage container and streams it back to the client. The asset is returned as a streaming response\n    to efficiently handle large files without loading them entirely into memory.\n\n    **Key Features:**\n    - **Streaming Response**: Efficient handling of large files through streaming\n    - **Access Control**: Only authenticated users can access their own assets\n    - **Format Support**: Supports various file types (images, documents, audio, video)\n    - **Direct Download**: Returns the raw file content with appropriate media type\n\n    **Query Parameters:**\n    - `asset_id`: Unique identifier of the asset to fetch (must match asset ID pattern)\n\n    **Supported Asset Types:**\n    - **Images**: JPEG, PNG, GIF, WebP, SVG\n    - **Documents**: PDF, TXT, DOC, DOCX\n    - **Audio**: MP3, WAV, M4A, OGG\n    - **Video**: MP4, MOV, AVI, WebM\n\n    **Use Cases:**\n    - Display images in user interfaces\n    - Download attached documents\n    - Stream audio/video content\n    - Access file attachments from events", "operationId": "fetch_asset_by_id_v3_asset_by_id__get", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "asset_id", "in": "query", "required": true, "schema": {"type": "string", "minLength": 1, "pattern": "^[A-Za-z0-9._-]+$", "description": "Unique identifier of the asset to fetch", "title": "Asset Id"}, "description": "Unique identifier of the asset to fetch"}, {"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "responses": {"200": {"description": "Asset retrieved and streamed successfully", "content": {"application/json": {"schema": {}}, "image/jpeg": {"example": "Binary image data"}, "application/pdf": {"example": "Binary PDF data"}, "audio/mpeg": {"example": "Binary audio data"}, "video/mp4": {"example": "Binary video data"}}}, "404": {"description": "Asset not found or access denied"}, "400": {"description": "Invalid asset ID format or malformed request"}, "401": {"description": "Authentication required"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/asset/url/": {"get": {"tags": ["assets"], "summary": "Fetch Asset URLs", "description": "Retrieve pre-signed URLs for multiple assets by their identifiers.\n\n    This endpoint generates pre-signed URLs for multiple assets from the user's storage container.\n    These URLs provide direct access to the assets without requiring authentication headers,\n    making them ideal for frontend applications, batch operations, and temporary access scenarios.\n\n    **Key Features:**\n    - **Batch Processing**: Fetch URLs for multiple assets in a single request\n    - **Pre-signed URLs**: Direct access URLs with built-in authentication\n    - **Temporary Access**: URLs have expiration times for security\n    - **Frontend Integration**: Perfect for displaying multiple images or files\n    - **No Streaming**: Returns URLs instead of file content for efficiency\n\n    **Query Parameters:**\n    - `asset_ids`: Array of asset identifiers to fetch URLs for (each must match asset ID pattern)\n\n    **Use Cases:**\n    - Display multiple images in galleries or carousels\n    - Batch download operations\n    - Frontend applications needing direct file access\n    - Temporary sharing of files\n    - Mobile applications with offline capabilities\n\n    **URL Characteristics:**\n    - **Time-limited**: URLs expire after a set period for security\n    - **Direct Access**: No additional authentication required\n    - **Original Format**: URLs point to original file format and quality\n    - **HTTPS**: All URLs use secure HTTPS protocol", "operationId": "fetch_asset_url_v3_asset_url__get", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "asset_ids", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string", "minLength": 1, "pattern": "^[A-Za-z0-9._-]+$"}, "description": "Array of asset IDs to fetch URLs for", "title": "Asset Ids"}, "description": "Array of asset IDs to fetch URLs for"}, {"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "responses": {"200": {"description": "Asset URLs retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FetchAssetUrlAPIOutput"}, "examples": [{"summary": "Asset URLs retrieved", "value": {"assets": {"asset_id_1": "https://storage.example.com/user123/asset_id_1?signature=...", "asset_id_2": "https://storage.example.com/user123/asset_id_2?signature=..."}}}]}}}, "404": {"description": "One or more assets not found or access denied"}, "400": {"description": "Invalid asset ID format or malformed request"}, "401": {"description": "Authentication required"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/document/by_query/": {"delete": {"tags": ["document"], "summary": "Delete user documents by query", "description": "Schedules asynchronous deletion of user's V3 documents matching the specified query criteria.\n\n    This endpoint schedules asynchronous deletion of the authenticated user's V3 documents\n    that match the provided query criteria. The deletion is performed in the background,\n    and the endpoint returns immediately with a 202 Accepted status.\n\n    **Query Structure:**\n    - **queries**: Array of typed queries, each targeting specific document types\n    - **types**: Document types to target (UseCase, Plan, Template, Event, etc.)\n    - **query**: Optional filter criteria using field-based queries and boolean operators\n\n    **Supported Query Types:**\n    - **exists**: Check if a field exists\n    - **values**: Match specific field values\n    - **pattern**: Text pattern matching\n    - **range**: Numeric or date range filtering\n    - **radius**: Geographic radius queries\n\n    **Boolean Operators:**\n    - **and**: All conditions must match\n    - **or**: Any condition must match\n    - **not**: Conditions must not match\n\n    **Security:**\n    - Only deletes documents owned by the authenticated user\n    - User UUID is automatically added to all queries for security\n    - Cannot delete documents from other users\n\n    **Behavior:**\n    - If query is null, deletes ALL user documents (equivalent to /all_data endpoint)\n    - Returns immediately after scheduling the deletion task\n    - Actual deletion happens asynchronously in the background\n    - Assets are only deleted when deleting all user data (query=null)", "operationId": "delete_user_data_by_query_endpoint_v3_document_by_query__delete", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/DocumentQueryAPI"}, {"type": "null"}], "description": "Query criteria to filter documents for deletion. If null, deletes all user documents.", "examples": [{"summary": "Delete specific document types", "description": "Delete all use cases and plans for the user", "value": {"queries": [{"types": ["UseCase", "Plan"]}]}}, {"summary": "Delete documents with field criteria", "description": "Delete archived use cases created before a specific date", "value": {"queries": [{"types": ["UseCase"], "query": {"type": "and", "queries": [{"type": "exists", "field_name": "archived_at"}, {"type": "range", "field_name": "created_at", "lt": "2024-01-01T00:00:00Z"}]}}]}}, {"summary": "Delete documents by tags", "description": "Delete all documents containing specific tags", "value": {"queries": [{"types": ["UseCase", "Plan", "Template"], "query": {"type": "values", "field_name": "tags", "values": ["deprecated", "old"]}}]}}], "title": "Query"}}}}, "responses": {"202": {"description": "Deletion task scheduled successfully", "content": {"application/json": {"schema": {}, "example": {}}}}, "404": {"description": "User not found"}, "400": {"description": "Bad request - invalid query structure or field names", "content": {"application/json": {"examples": [{"summary": "Invalid query structure", "value": {"detail": "Invalid query structure: missing required field 'types'"}}, {"summary": "Invalid field names", "value": {"detail": "Invalid field name in query: unknown_field"}}]}}}, "401": {"description": "Authentication required"}, "409": {"description": "Conflict - another deletion or upload operation is already in progress", "content": {"application/json": {"example": {"detail": "Another data operation is currently in progress. Please wait for it to complete."}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/document/all_data/": {"delete": {"tags": ["document"], "summary": "Delete all user documents and assets", "description": "Schedule complete deletion of all user's V3 documents and associated assets.\n\n    This endpoint schedules comprehensive deletion of all the authenticated user's V3 data,\n    including all document types (events, plans, use cases, templates, records) and associated\n    file assets. This is the most complete data deletion operation available.\n\n    **Key Features:**\n    - **Complete Data Removal**: Deletes all user documents and associated files\n    - **Asynchronous Processing**: Returns immediately while deletion happens in background\n    - **Asset Cleanup**: Automatically removes all uploaded files and media\n    - **Atomic Operation**: All data is deleted together or the operation fails\n    - **GDPR Compliance**: Supports right to be forgotten requirements\n\n    **What Gets Deleted:**\n    - **All V3 Documents**: Events, Plans, Use Cases, Templates, and Records\n    - **File Assets**: All uploaded files, images, audio, video, and documents\n    - **Metadata**: All document metadata and system properties\n    - **Relationships**: All document relationships and references\n    - **User Preferences**: Associated user settings and configurations\n\n    **Security and Safety:**\n    - Only deletes data owned by the authenticated user\n    - Cannot affect other users' data\n    - Requires valid authentication token\n    - Operation cannot be undone once started\n\n    **Use Cases:**\n    - Account closure and data purging\n    - GDPR compliance (right to be forgotten)\n    - Complete data reset for testing environments\n    - Privacy-focused comprehensive data cleanup\n    - Migration preparation for data export/import\n\n    **Important Considerations:**\n    - **Irreversible**: This operation cannot be undone\n    - **Complete**: All user data will be permanently deleted\n    - **Alternative**: Consider `/by_query` endpoint for selective deletion\n    - **Timing**: Assets may take additional time to be fully removed from storage\n    - **Background**: Deletion happens asynchronously after endpoint returns", "operationId": "delete_all_user_data_endpoint_v3_document_all_data__delete", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "responses": {"202": {"description": "Deletion task scheduled successfully", "content": {"application/json": {"schema": {}, "example": {}}}}, "404": {"description": "User not found"}, "401": {"description": "Authentication required"}, "409": {"description": "Conflict - another deletion or upload operation is already in progress", "content": {"application/json": {"example": {"detail": "Another data operation is currently in progress. Please wait for it to complete."}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/event/feed/": {"post": {"tags": ["event"], "summary": "Get Event Feed", "description": "Retrieve a paginated feed of user events sorted by timestamp in descending order.\n\n    This endpoint provides a chronological feed of user events with pagination support.\n    Events are returned in reverse chronological order (newest first) and can be filtered\n    by data type and organization. The feed uses continuation tokens for efficient pagination.\n\n    **Key Features:**\n    - **Chronological Ordering**: Events sorted by timestamp (newest first)\n    - **Pagination**: Efficient pagination using continuation tokens\n    - **Filtering**: Filter by data type and organization\n    - **Immutable Filters**: Initial filters remain consistent across paginated requests\n    - **Flexible Limits**: Adjustable page size and date range for each request\n\n    **Query Parameters:**\n    - `limit`: Number of events per page (configurable)\n    - `continuation_token`: Token for retrieving subsequent pages\n    - `data_type`: Filter events by specific data types\n    - `organization`: Filter events by data source organization\n    - `range`: Date range filter for event timestamps\n\n    **Pagination Behavior:**\n    - Initial request establishes filter criteria (data_type, organization)\n    - Subsequent requests with continuation_token maintain these filters\n    - Limit and range parameters can be modified for each request", "operationId": "event_feed_endpoint_v3_event_feed__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 10000, "exclusiveMinimum": 0, "default": 100, "title": "Limit"}}, {"name": "continuation_token", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Continuation Token"}}, {"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EventFeedAPIRequestInput"}}}}, "responses": {"200": {"description": "Events retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EventFeedAPIResponse"}, "example": {"items": [{"id": "123e4567-e89b-12d3-a456-************", "name": "Morning workout", "timestamp": "2024-01-15T08:30:00Z", "type": "exercise"}], "continuation_token": "eyJsYXN0X3RpbWVzdGFtcCI6IjIwMjQtMDEtMTVUMDg6MzA6MDBaIn0="}}}}, "404": {"description": "Not found"}, "400": {"description": "Invalid request parameters or malformed continuation token"}, "401": {"description": "Authentication required"}, "422": {"description": "Validation error in request parameters"}}}}, "/v3/event/": {"post": {"tags": ["event"], "summary": "Create Events", "description": "Create new events in the system for tracking personal data and activities.\n\n    This endpoint allows users to insert one or more events into their personal data collection.\n    Events can be of various types including core events, notes, symptoms, emotions, medications,\n    sleep records, and more. Each event must include a name, timestamp, and type-specific data.\n\n    **Key Features:**\n    - **Multiple Event Types**: Support for symptoms, medications, exercises, notes, and more\n    - **Bulk Creation**: Create multiple events in a single request\n    - **Template Integration**: Events can be created from predefined templates\n    - **Asset Support**: Attach files, images, or other media to events\n    - **Duplicate Detection**: Prevents creation of identical events\n    - **Flexible Structure**: Type-specific fields for detailed event data\n\n    **Supported Event Types:**\n    - **Symptoms**: Track health symptoms with ratings, body parts, and categories\n    - **Medications**: Record medication intake with dosage and timing\n    - **Exercise**: Log physical activities with duration and intensity\n    - **Notes**: General text-based observations and thoughts\n    - **Sleep**: Track sleep patterns and quality\n    - **Nutrition**: Record meals and dietary information\n    - **Emotions**: Log emotional states and mood tracking\n\n    **Request Body:**\n    - `documents`: Array of event objects to insert (minimum 1 required)\n      - `name`: Event name (1-200 characters, required)\n      - `timestamp`: ISO 8601 datetime when the event occurred (required)\n      - `type`: Event type (required)\n      - Type-specific fields (category, rating, etc.)\n      - `end_time`: For events with duration (optional)\n      - `tags`: Array of string tags for categorization (optional)\n      - `note`: Additional text description (optional)\n      - `assets`: Array of file attachments (optional)\n      - `template_id`: UUID of template used to create event (optional)", "operationId": "insert_event_endpoint_v3_event__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsertEventAPIRequestInput"}}}}, "responses": {"200": {"description": "Events successfully created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_Annotated_Union_BloodGlucoseAPIOutput__BloodPressureAPIOutput__BodyMetricAPIOutput__AudioAPIOutput__ContentAPIOutput__ImageAPIOutput__InteractiveAPIOutput__TextAPIOutput__VideoAPIOutput__ExerciseAPIOutput__CardioAPIOutput__StrengthAPIOutput__EmotionAPIOutput__StressAPIOutput__DrinkAPIOutput__FoodAPIOutput__SupplementAPIOutput__CoreEventAPIOutput__SleepV3APIOutput__EventGroupAPIOutput__NoteAPIOutput__SymptomAPIOutput__MedicationAPIOutput___FieldInfo_annotation_NoneType__required_True__discriminator__type____"}, "example": {"documents": [{"id": "123e4567-e89b-12d3-a456-************", "name": "Morning headache", "timestamp": "2024-01-15T08:30:00Z", "type": "symptom", "category": "headache", "rating": 6, "body_parts": ["head"], "tags": ["morning", "stress"], "system_properties": {"created_at": "2024-01-15T08:30:00Z", "updated_at": "2024-01-15T08:30:00Z"}}]}}}}, "404": {"description": "Not found"}, "400": {"description": "Bad request - duplicate events or validation errors", "content": {"application/json": {"examples": [{"summary": "Duplicate events", "value": {"detail": "event duplicates found in the input payload"}}, {"summary": "Validation error", "value": {"detail": "Invalid event data: timestamp is required"}}]}}}, "401": {"description": "Authentication required"}, "422": {"description": "Validation error in request body"}}}, "patch": {"tags": ["event"], "summary": "Update Events", "description": "Update existing events with new information while preserving their identity and system properties.\n\n    This endpoint allows users to modify one or more existing events in their personal data collection.\n    Users can update event properties such as name, timestamp, ratings, categories, and other\n    type-specific fields. The event ID and type must be provided to identify which events to update.\n\n    **Key Features:**\n    - **Bulk Updates**: Modify multiple events in a single request\n    - **Partial Updates**: Only specified fields are updated, others remain unchanged\n    - **Type Validation**: Event type must match existing event for security\n    - **Duplicate Detection**: Prevents updates that would create duplicate events\n    - **Atomic Operations**: All updates in a batch succeed or fail together\n\n    **Request Body:**\n    - `documents`: Array of event objects to update (minimum 1 required)\n      - `id`: UUID of the existing event to update (required)\n      - `name`: Updated event name (1-200 characters, required)\n      - `timestamp`: Updated ISO 8601 datetime (required)\n      - `type`: Event type (must match existing event type, required)\n      - Type-specific fields to update (category, rating, etc.)\n      - `end_time`: Updated end time for events with duration (optional)\n      - `tags`: Updated array of string tags (optional)\n      - `plan_extension`: Updated plan extension data (optional)", "operationId": "update_event_endpoint_v3_event__patch", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateEventAPIRequestInput"}}}}, "responses": {"200": {"description": "Events successfully updated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_Annotated_Union_BloodGlucoseAPIOutput__BloodPressureAPIOutput__BodyMetricAPIOutput__AudioAPIOutput__ContentAPIOutput__ImageAPIOutput__InteractiveAPIOutput__TextAPIOutput__VideoAPIOutput__ExerciseAPIOutput__CardioAPIOutput__StrengthAPIOutput__EmotionAPIOutput__StressAPIOutput__DrinkAPIOutput__FoodAPIOutput__SupplementAPIOutput__CoreEventAPIOutput__SleepV3APIOutput__EventGroupAPIOutput__NoteAPIOutput__SymptomAPIOutput__MedicationAPIOutput___FieldInfo_annotation_NoneType__required_True__discriminator__type____"}, "examples": [{"summary": "Event update success", "value": {"documents": [{"id": "123e4567-e89b-12d3-a456-************", "name": "Updated headache severity", "timestamp": "2024-01-15T08:30:00Z", "type": "symptom", "category": "headache", "rating": 8, "tags": ["morning", "severe"], "system_properties": {"created_at": "2024-01-15T08:00:00Z", "updated_at": "2024-01-15T09:00:00Z"}}]}}]}}}, "404": {"description": "One or more events not found"}, "400": {"description": "Bad request - duplicate events, validation errors, or operation not allowed", "content": {"application/json": {"examples": [{"summary": "Duplicate events", "value": {"detail": "event duplicates found in the input payload"}}, {"summary": "Type validation error", "value": {"detail": "Event type does not match existing event"}}]}}}, "401": {"description": "Authentication required"}, "422": {"description": "Validation error in request body"}}}, "delete": {"tags": ["event"], "summary": "Delete Events", "description": "Delete existing events from the system permanently.\n\n    This endpoint allows users to permanently remove one or more events from their personal\n    data collection. Once deleted, events cannot be recovered. Users must provide the event\n    ID and type to identify which events to delete for security verification.\n\n    **Key Features:**\n    - **Permanent Deletion**: Events are completely removed and cannot be recovered\n    - **Bulk Operations**: Delete multiple events in a single request\n    - **Type Verification**: Event type must match existing event for security\n    - **Asset Cleanup**: Associated files and assets are automatically deleted\n    - **Ownership Protection**: Users can only delete their own events\n\n    **Security Measures:**\n    - Event type verification prevents accidental deletions\n    - User ownership validation ensures data isolation\n    - Atomic operations ensure all deletions succeed or fail together\n\n    **Request Body:**\n    - `documents`: Array of event deletion objects (minimum 1 required)\n      - `id`: UUID of the existing event to delete (required)\n      - `type`: Event type (must match existing event type for verification, required)\n\n    **Important Considerations:**\n    - **Irreversible**: Deletion is permanent and cannot be undone\n    - **Asset Cleanup**: All associated files and media will be deleted\n    - **Group Events**: Events that are part of a group may have deletion restrictions\n    - **Data Integrity**: Related data references may be affected", "operationId": "delete_event_endpoint_v3_event__delete", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteEventAPIRequestInput", "description": "Event deletion request containing event IDs and types for verification"}, "example": {"documents": [{"id": "123e4567-e89b-12d3-a456-************", "type": "symptom"}, {"id": "456e7890-e89b-12d3-a456-426614174001", "type": "note"}]}}}}, "responses": {"200": {"description": "Events successfully deleted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsIdsResponse"}, "examples": [{"summary": "Successful deletion", "value": {"document_ids": ["123e4567-e89b-12d3-a456-************", "456e7890-e89b-12d3-a456-426614174001"]}}]}}}, "404": {"description": "One or more events not found or access denied"}, "400": {"description": "Bad request - invalid data or operation not allowed", "content": {"application/json": {"examples": [{"summary": "Event type mismatch", "value": {"detail": "Event type does not match existing event"}}, {"summary": "Operation not allowed", "value": {"detail": "Cannot delete event: event is part of a protected group"}}]}}}, "401": {"description": "Authentication required"}, "422": {"description": "Validation error in request body"}}}}, "/v3/event/modify_assets/": {"patch": {"tags": ["event"], "summary": "Modify Event Assets", "description": "Modify asset attachments for existing events by adding, removing, or updating file attachments.\n\n    This endpoint allows users to manage file attachments (assets) associated with existing events.\n    Assets can include images, audio recordings, videos, or other files that provide additional\n    context or evidence for the event. Operations include adding new assets, removing existing ones,\n    or updating asset metadata.\n\n    **Key Features:**\n    - **Asset Management**: Add, remove, or update file attachments on events\n    - **Multiple Formats**: Support for images, audio, video, and document files\n    - **Bulk Operations**: Modify assets for multiple events in a single request\n    - **Type Validation**: Event type must match existing event for security\n    - **Size Limits**: Individual assets limited to 10MB for performance\n\n    **Supported Asset Types:**\n    - **Images**: JPEG, PNG, GIF, WebP for photos and visual documentation\n    - **Audio**: MP3, WAV, M4A, OGG for voice notes and audio recordings\n    - **Video**: MP4, MOV, AVI, WebM for video documentation\n    - **Documents**: PDF, TXT for text-based attachments\n\n    **Asset Operations:**\n    - **Add**: Upload new assets with base64-encoded data\n    - **Remove**: Delete existing assets by asset ID\n    - **Update**: Modify asset metadata and properties\n\n    **Request Body:**\n    - `documents`: Array of event asset modification objects (minimum 1 required)\n      - `id`: UUID of the existing event to modify (required)\n      - `type`: Event type (must match existing event type, required)\n      - `assets`: Array of asset operations to perform", "operationId": "modify_event_assets_endpoint_v3_event_modify_assets__patch", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ModifyEventAssetsAPIRequestInput", "description": "Event asset modification request containing event IDs and asset operations"}, "example": {"documents": [{"id": "123e4567-e89b-12d3-a456-************", "type": "symptom", "assets": [{"asset_type": "image", "data": "base64-encoded-image-data...", "filename": "headache_photo.jpg"}]}]}}}}, "responses": {"200": {"description": "Event assets successfully modified", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_Annotated_Union_BloodGlucoseAPIOutput__BloodPressureAPIOutput__BodyMetricAPIOutput__AudioAPIOutput__ContentAPIOutput__ImageAPIOutput__InteractiveAPIOutput__TextAPIOutput__VideoAPIOutput__ExerciseAPIOutput__CardioAPIOutput__StrengthAPIOutput__EmotionAPIOutput__StressAPIOutput__DrinkAPIOutput__FoodAPIOutput__SupplementAPIOutput__CoreEventAPIOutput__SleepV3APIOutput__EventGroupAPIOutput__NoteAPIOutput__SymptomAPIOutput__MedicationAPIOutput___FieldInfo_annotation_NoneType__required_True__discriminator__type____"}, "examples": [{"summary": "Asset modification success", "value": {"documents": [{"id": "123e4567-e89b-12d3-a456-************", "name": "Morning headache", "timestamp": "2024-01-15T08:30:00Z", "type": "symptom", "assets": [{"asset_id": "asset_123", "asset_type": "image", "filename": "headache_photo.jpg", "size": 1024000}], "system_properties": {"created_at": "2024-01-15T08:00:00Z", "updated_at": "2024-01-15T09:00:00Z"}}]}}]}}}, "404": {"description": "Event not found or access denied"}, "400": {"description": "Bad request - invalid data, duplicate events, or unsupported asset format", "content": {"application/json": {"examples": [{"summary": "Duplicate events", "value": {"detail": "event duplicates found in the input payload"}}, {"summary": "Unsupported asset format", "value": {"detail": "Asset format not supported. Supported formats: JPEG, PNG, GIF, WebP, MP3, WAV, M4A, OGG, MP4, MOV, AVI, WebM, PDF, TXT"}}]}}}, "401": {"description": "Authentication required"}, "413": {"description": "Asset file too large", "content": {"application/json": {"examples": [{"summary": "File size exceeded", "value": {"detail": "Asset file size exceeds maximum limit of 10MB"}}]}}}, "422": {"description": "Validation error in request body"}}}}, "/v3/use_case/search/": {"post": {"tags": ["use case"], "summary": "Search Use Cases", "description": "Search and retrieve use cases with advanced filtering, sorting, and pagination capabilities.\n\n    This endpoint allows users to search through their use cases using various filters,\n    sorting options, and pagination. Use cases represent specific scenarios or workflows\n    that users want to track and manage in their personal data collection.\n\n    **Key Features:**\n    - **Advanced Filtering**: Query by name, tags, archived status, and other properties\n    - **Flexible Sorting**: Sort by any use case field (created_at, name, updated_at, etc.)\n    - **Pagination**: Use limit and continuation_token for efficient data retrieval\n    - **Boolean Queries**: Combine multiple filters with AND/OR logic\n\n    **Query Parameters:**\n    - `limit`: Maximum number of results to return (1-10000, default: 100)\n    - `continuation_token`: Token for pagination to get next page of results\n\n    **Request Body:**\n    - `sort`: Sorting configuration with field name and order\n    - `query`: Advanced query filters using boolean logic and field-specific conditions", "operationId": "search_use_cases_endpoint_v3_use_case_search__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 10000, "exclusiveMinimum": 0, "default": 100, "title": "Limit"}}, {"name": "continuation_token", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Continuation Token"}}, {"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"content": {"application/json": {"schema": {"anyOf": [{"$ref": "#/components/schemas/SearchUseCaseRequestInput"}, {"type": "null"}], "title": "Request Input"}}}}, "responses": {"200": {"description": "Use cases found and returned successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_UseCaseAPIOutput_"}, "example": {"documents": [{"doc_id": "123e4567-e89b-12d3-a456-************", "name": "Daily Workout Routine", "type": "use_case", "tags": ["fitness", "daily", "routine"], "system_properties": {"created_at": "2024-01-15T08:30:00Z", "updated_at": "2024-01-15T08:30:00Z"}}]}}}}, "404": {"description": "Not found"}, "204": {"description": "No use cases found matching the search criteria"}, "400": {"description": "Invalid search parameters or malformed request"}, "401": {"description": "Authentication required"}, "422": {"description": "Validation error in request parameters"}}}}, "/v3/use_case/archive/": {"patch": {"tags": ["use case"], "summary": "Archive Use Cases", "description": "Archive use cases to remove them from active use while preserving their data and history.\n\n    This endpoint allows you to archive one or more use cases, marking them as inactive without\n    permanently deleting them. Archived use cases are excluded from search results by default\n    but can still be retrieved if specifically requested, and their data remains intact for\n    historical reference.\n\n    **Key Features:**\n    - **Soft Deletion**: Use cases are marked as archived, not permanently deleted\n    - **Data Preservation**: All use case data, metadata, and relationships are preserved\n    - **Search Exclusion**: Archived use cases are excluded from default search results\n    - **Batch Processing**: Multiple use cases can be archived in a single operation\n    - **Atomic Operations**: All use cases in the request are archived together or the operation fails\n\n    **Query Parameters:**\n    - `use_case_ids`: Array of UUID strings identifying the use cases to archive", "operationId": "archive_use_cases_endpoint_v3_use_case_archive__patch", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "use_case_ids", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "List of use case IDs to archive", "title": "Use Case Ids"}, "description": "List of use case IDs to archive"}, {"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "responses": {"200": {"description": "Use cases successfully archived", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_UseCaseAPIOutput_"}, "example": {"documents": [{"doc_id": "123e4567-e89b-12d3-a456-************", "name": "Old Workout Routine", "type": "use_case", "tags": ["fitness", "outdated"], "archived_at": "2024-01-15T10:30:00Z", "system_properties": {"created_at": "2024-01-01T08:30:00Z", "updated_at": "2024-01-15T10:30:00Z"}}]}}}}, "404": {"description": "One or more use cases not found"}, "400": {"description": "Invalid use case IDs or operation not allowed"}, "401": {"description": "Authentication required"}, "422": {"description": "Validation error in query parameters"}}}}, "/v3/use_case/": {"patch": {"tags": ["use case"], "summary": "Update Use Cases", "description": "Update existing use cases with new information while preserving their identity and system properties.\n\n    This endpoint allows users to modify one or more existing use cases in their personal collection.\n    Users can update properties such as name and tags while preserving the use case's identity\n    and system-managed properties. Each use case to update must include its doc_id for identification.\n\n    **Key Features:**\n    - **Bulk Updates**: Modify multiple use cases in a single request\n    - **Partial Updates**: Only specified fields are updated, others remain unchanged\n    - **Identity Preservation**: Use case IDs remain unchanged during updates\n    - **Duplicate Detection**: Prevents updates that would create duplicate use cases\n    - **Atomic Operations**: All updates in a batch succeed or fail together\n\n    **Request Body:**\n    - `documents`: Array of use case update objects (minimum 1 required)\n      - `doc_id`: UUID of the existing use case to update (required)\n      - `name`: New name for the use case (required, minimum 1 character)\n      - `tags`: Array of tag strings (optional, maximum defined by system limits)", "operationId": "update_use_cases_endpoint_v3_use_case__patch", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUseCaseAPIRequestInput"}}}}, "responses": {"200": {"description": "Use cases successfully updated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_UseCaseAPIOutput_"}, "example": {"documents": [{"doc_id": "123e4567-e89b-12d3-a456-************", "name": "Updated Workout Routine", "type": "use_case", "tags": ["fitness", "updated", "routine"], "system_properties": {"created_at": "2024-01-01T08:30:00Z", "updated_at": "2024-01-15T10:30:00Z"}}]}}}}, "404": {"description": "One or more use cases not found"}, "400": {"description": "Bad request - duplicate use cases, validation errors, or operation not allowed", "content": {"application/json": {"examples": [{"summary": "Duplicate use cases", "value": {"detail": "use case duplicates found in the update payload"}}, {"summary": "Validation error", "value": {"detail": "Invalid use case data: doc_id not found"}}]}}}, "401": {"description": "Authentication required"}, "422": {"description": "Validation error in request body"}}}, "post": {"tags": ["use case"], "summary": "Create Use Cases", "description": "Create new use cases in the system for tracking scenarios, workflows, and patterns.\n\n    This endpoint allows users to insert one or more new use cases into their personal data collection.\n    Use cases represent specific scenarios, workflows, or patterns that users want to track and manage.\n    Each use case must include a name and can optionally include tags for categorization and organization.\n\n    **Key Features:**\n    - **Bulk Creation**: Create multiple use cases in a single request\n    - **Duplicate Detection**: Prevents creation of identical use cases\n    - **Automatic ID Generation**: Each use case is assigned a unique identifier\n    - **Tag Support**: Custom tags for organization and categorization\n    - **User Association**: Use cases are linked to the authenticated user's account\n\n    **Request Body:**\n    - `documents`: Array of use case objects to create (minimum 1 required)\n      - `name`: Name of the use case (required, minimum 1 character)\n      - `tags`: Array of tag strings for categorization (optional, maximum defined by system limits)\n\n    **Common Use Cases:**\n    - Track daily routines and habits\n    - Define workout or exercise patterns\n    - Organize project workflows\n    - Categorize health and wellness scenarios\n    - Create templates for recurring activities", "operationId": "insert_use_cases_endpoint_v3_use_case__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsertUseCaseAPIRequestInput"}}}}, "responses": {"200": {"description": "Use cases successfully created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_UseCaseAPIOutput_"}, "examples": [{"summary": "Use case creation success", "value": {"documents": [{"doc_id": "123e4567-e89b-12d3-a456-************", "name": "Daily Workout Routine", "type": "use_case", "tags": ["fitness", "daily", "routine"], "system_properties": {"created_at": "2024-01-15T08:30:00Z", "updated_at": "2024-01-15T08:30:00Z"}}]}}]}}}, "404": {"description": "Not found"}, "400": {"description": "Bad request - duplicate use cases or validation errors", "content": {"application/json": {"examples": [{"summary": "Duplicate use case", "value": {"detail": "Duplicate use case detected. A use case with the same details already exists in the system."}}, {"summary": "Validation error", "value": {"detail": "Invalid use case data: name is required"}}]}}}, "401": {"description": "Authentication required"}, "422": {"description": "Validation error in request body"}}}}, "/v3/aggs/date_histogram/": {"post": {"tags": ["aggregate"], "summary": "Generate date histogram aggregations", "description": "Creates time-based histogram aggregations of documents with configurable time intervals and field aggregations.\n\n    This endpoint groups documents into time buckets based on their timestamps and applies aggregation methods\n    (sum, avg, min, max, count) to specified fields within each bucket. Useful for analyzing trends over time,\n    creating time series visualizations, and understanding temporal patterns in data.\n\n    **Key Features:**\n    - Configurable time intervals (minutes, hours, days, weeks, months, years)\n    - Multiple aggregation methods per field (sum, average, min, max, count)\n    - Timezone support for proper time bucket alignment\n    - Moving average calculations with configurable window sizes\n    - Document count per time bucket\n\n    **Returns:** Array of time buckets with timestamp ranges, document counts, and field aggregation results.", "operationId": "date_histogram_endpoint_v3_aggs_date_histogram__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataHistogramAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataHistogramAPIOutput"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/aggs/frequency_distribution/": {"post": {"tags": ["aggregate"], "summary": "Generate frequency distribution of field values", "description": "Analyzes the frequency distribution of unique values for a specified field across document types.\n\n    This endpoint counts how many times each unique value appears in the specified field, providing insights\n    into data distribution patterns. Useful for understanding value popularity, identifying outliers,\n    and creating categorical data visualizations.\n\n    **Key Features:**\n    - Counts occurrences of unique values in any document field\n    - Supports multiple document types in a single query\n    - Results grouped by document type for clear categorization\n    - Sorted by frequency (most common values first)\n    - Configurable result size limit (up to 100,000 unique values)\n\n    **Use Cases:**\n    - Analyzing category distributions (e.g., event types, tags, ratings)\n    - Finding most common values in datasets\n    - Data quality assessment and outlier detection\n    - Creating frequency charts and categorical visualizations\n\n    **Returns:** Dictionary mapping document types to value-frequency pairs, sorted by frequency descending.", "operationId": "frequency_distribution_endpoint_v3_aggs_frequency_distribution__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrequencyDistributionAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FrequencyDistributionAPIOutput"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/aggs/calendar_histogram_aggregation/": {"post": {"tags": ["aggregate"], "summary": "Generate calendar-based histogram aggregations", "description": "Creates histogram aggregations based on calendar patterns (months, weekdays, hours, lunar phases, etc.)\n    with field value aggregations for each calendar bucket.\n\n    This endpoint groups documents by calendar-based patterns and applies aggregation methods to specified\n    fields within each calendar bucket. Perfect for discovering cyclical patterns, seasonal trends, and\n    time-of-day behaviors in your data.\n\n    **Calendar Aggregation Types:**\n    - **month_names**: Groups by calendar months (January, February, etc.)\n    - **weekdays**: Groups by days of the week (Monday, Tuesday, etc.)\n    - **hours_in_day**: Groups by hours (0-23) for daily pattern analysis\n    - **days_of_month**: Groups by day of month (1-31) for monthly patterns\n    - **parts_of_month**: Groups by month sections (1-5) for start/mid/end analysis\n    - **lunar_phases**: Groups by moon phases (New Moon, First Quarter, Full Moon, Last Quarter)\n\n    **Key Features:**\n    - Multiple aggregation methods (sum, average, min, max, count)\n    - Optional null value filling for complete calendar coverage\n    - Document count per calendar bucket\n    - Aggregated field values per bucket\n\n    **Use Cases:**\n    - Seasonal trend analysis (monthly patterns)\n    - Daily routine analysis (hourly patterns)\n    - Weekly behavior patterns (weekday analysis)\n    - Lunar cycle correlations\n    - Monthly distribution analysis\n\n    **Returns:** Array of calendar buckets with aggregation keys, document counts, and field aggregation results.", "operationId": "calendar_histogram_aggregation_endpoint_v3_aggs_calendar_histogram_aggregation__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalendarHistogramAggregationAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalendarHistogramAggregationAPIOutput"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/aggs/calendar_frequency_distribution/": {"post": {"tags": ["aggregate"], "summary": "Generate calendar-based frequency distributions", "description": "Analyzes document frequency distributions based on calendar patterns to identify cyclical behaviors\n    and temporal patterns in data occurrence.\n\n    This endpoint counts how many documents occur within each calendar-based bucket (months, weekdays,\n    hours, etc.), revealing patterns like seasonal activity, weekly routines, daily habits, or lunar\n    cycle correlations. Unlike histogram aggregations, this focuses purely on document counts rather\n    than field value aggregations.\n\n    **Calendar Aggregation Types:**\n    - **month_names**: Document counts by calendar months (January, February, etc.)\n    - **weekdays**: Document counts by days of the week (Monday, Tuesday, etc.)\n    - **hours_in_day**: Document counts by hours (0-23) for daily activity patterns\n    - **days_of_month**: Document counts by day of month (1-31) for monthly patterns\n    - **parts_of_month**: Document counts by month sections (1-5) for start/mid/end analysis\n    - **lunar_phases**: Document counts by moon phases (New Moon, First Quarter, Full Moon, Last Quarter)\n    - **time_between**: Document counts by time intervals between consecutive events\n\n    **Key Features:**\n    - Pure frequency counting (no field value aggregation)\n    - Optional null value filling for complete calendar coverage\n    - Specialized time-between analysis for event spacing patterns\n    - Sorted results for easy pattern identification\n\n    **Use Cases:**\n    - Activity pattern discovery (when do events typically occur?)\n    - Seasonal behavior analysis\n    - Daily/weekly routine identification\n    - Event clustering and spacing analysis\n    - Lunar cycle correlation studies\n    - Data collection pattern validation\n\n    **Returns:** Array of calendar buckets with aggregation keys and document frequency counts.", "operationId": "calendar_frequency_distribution_endpoint_v3_aggs_calendar_frequency_distribution__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalendarFrequencyDistributionAPIRequestInput"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalendarFrequencyDistributionAPIOutput"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/ai/suggest/event/": {"post": {"tags": ["ai"], "summary": "Suggest Event from Natural Language", "description": "Generate structured event data from natural language input using AI.\n\n    This endpoint processes a natural language query and returns a structured event that can be\n    inserted into the user's timeline. The AI analyzes the query to:\n\n    **Key Features:**\n    - **Intelligent Classification**: Automatically determines the appropriate event category\n    - **Temporal Extraction**: Identifies timestamps, durations, and end times from natural language\n    - **User Context Awareness**: Uses the user's timezone and existing templates for better suggestions\n    - **Structured Output**: Returns properly formatted event data ready for insertion\n    - **Multi-language Support**: Processes queries in various natural language formats\n\n    **AI Capabilities:**\n    - **Event Type Detection**: Classifies as exercise, nutrition, symptom, medication, note, etc.\n    - **Time Parsing**: Understands relative times (\"yesterday\", \"this morning\", \"in 1 hour\")\n    - **Duration Extraction**: Identifies activity durations and end times\n    - **Context Integration**: Leverages user's historical data and preferences\n    - **Data Validation**: Ensures generated events meet schema requirements\n\n    **Supported Query Types:**\n    - **Nutrition**: \"I had a big mac for lunch yesterday\", \"Ate 2 slices of pizza at 7pm\"\n    - **Exercise**: \"Play football at 10am for 1 hour\", \"30 minute run in the park\"\n    - **Symptoms**: \"Feeling tired and headache this morning\", \"Back pain started 2 hours ago\"\n    - **Medications**: \"Took 2 ibuprofen tablets for headache\", \"Morning vitamins at 8am\"\n    - **General Notes**: \"Had a great meeting with the team\", \"Feeling stressed about deadline\"\n\n    **Response Structure:**\n    The response includes all necessary fields to create an event, with timestamps adjusted\n    to the user's timezone and content structured according to the detected event type.", "operationId": "suggest_event_endpoint_v3_ai_suggest_event__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuggestEventRequestInput", "description": "Natural language query describing the event to be suggested"}}}}, "responses": {"200": {"description": "Event suggestion generated successfully", "content": {"application/json": {"schema": {"oneOf": [{"$ref": "#/components/schemas/InsertBloodGlucoseInput-Output"}, {"$ref": "#/components/schemas/InsertBloodPressureInput-Output"}, {"$ref": "#/components/schemas/InsertBodyMetricInput-Output"}, {"$ref": "#/components/schemas/InsertAudioInput-Output"}, {"$ref": "#/components/schemas/InsertContentInput-Output"}, {"$ref": "#/components/schemas/InsertImageInput-Output"}, {"$ref": "#/components/schemas/InsertInteractiveInput-Output"}, {"$ref": "#/components/schemas/InsertTextInput-Output"}, {"$ref": "#/components/schemas/InsertVideoInput-Output"}, {"$ref": "#/components/schemas/InsertCardioInput-Output"}, {"$ref": "#/components/schemas/InsertExerciseInput-Output"}, {"$ref": "#/components/schemas/InsertStrengthInput-Output"}, {"$ref": "#/components/schemas/InsertEmotionInput-Output"}, {"$ref": "#/components/schemas/InsertStressInput-Output"}, {"$ref": "#/components/schemas/InsertDrinkInput-Output"}, {"$ref": "#/components/schemas/InsertFoodInput-Output"}, {"$ref": "#/components/schemas/InsertSupplementInput-Output"}, {"$ref": "#/components/schemas/InsertCoreEventInput-Output"}, {"$ref": "#/components/schemas/InsertSleepV3Input-Output"}, {"$ref": "#/components/schemas/InsertEventGroupInput-Output"}, {"$ref": "#/components/schemas/InsertNoteInput-Output"}, {"$ref": "#/components/schemas/InsertSymptomInput-Output"}, {"$ref": "#/components/schemas/InsertMedicationInput-Output"}], "discriminator": {"propertyName": "type", "mapping": {"blood_glucose": "#/components/schemas/InsertBloodGlucoseInput-Output", "blood_pressure": "#/components/schemas/InsertBloodPressureInput-Output", "body_metric": "#/components/schemas/InsertBodyMetricInput-Output", "audio": "#/components/schemas/InsertAudioInput-Output", "content": "#/components/schemas/InsertContentInput-Output", "image": "#/components/schemas/InsertImageInput-Output", "interactive": "#/components/schemas/InsertInteractiveInput-Output", "text": "#/components/schemas/InsertTextInput-Output", "video": "#/components/schemas/InsertVideoInput-Output", "cardio": "#/components/schemas/InsertCardioInput-Output", "exercise": "#/components/schemas/InsertExerciseInput-Output", "strength": "#/components/schemas/InsertStrengthInput-Output", "emotion": "#/components/schemas/InsertEmotionInput-Output", "stress": "#/components/schemas/InsertStressInput-Output", "drink": "#/components/schemas/InsertDrinkInput-Output", "food": "#/components/schemas/InsertFoodInput-Output", "supplement": "#/components/schemas/InsertSupplementInput-Output", "core_event": "#/components/schemas/InsertCoreEventInput-Output", "sleep": "#/components/schemas/InsertSleepV3Input-Output", "event_group": "#/components/schemas/InsertEventGroupInput-Output", "note": "#/components/schemas/InsertNoteInput-Output", "symptom": "#/components/schemas/InsertSymptomInput-Output", "medication": "#/components/schemas/InsertMedicationInput-Output"}}, "title": "Response Suggest Event Endpoint V3 Ai Suggest Event  Post"}, "examples": [{"summary": "Exercise event suggestion", "value": {"name": "Football", "timestamp": "2024-01-15T10:00:00Z", "end_time": "2024-01-15T11:00:00Z", "type": "exercise", "category": "team_sports", "duration_minutes": 60, "intensity": "moderate"}}, {"summary": "Nutrition event suggestion", "value": {"name": "Big Mac", "timestamp": "2024-01-14T12:00:00Z", "type": "nutrition", "category": "fast_food", "meal_type": "lunch", "calories": 550}}]}}}, "404": {"description": "Not found"}, "400": {"description": "Bad request - invalid query or unable to parse natural language input", "content": {"application/json": {"examples": [{"summary": "Unparseable query", "value": {"detail": "Unable to extract meaningful event data from the provided query"}}, {"summary": "Invalid input", "value": {"detail": "Query must contain at least one recognizable event or activity"}}]}}}, "401": {"description": "Authentication required"}, "422": {"description": "Validation error in request body"}, "500": {"description": "Internal server error - AI service unavailable", "content": {"application/json": {"examples": [{"summary": "AI service error", "value": {"detail": "AI suggestion service is temporarily unavailable"}}]}}}}}}, "/v3record/": {"post": {"tags": ["record"], "summary": "Create Records", "description": "Create new records in the system for tracking structured data with precise timing and metadata.\n\n    This endpoint allows users to insert one or more records into their personal data collection.\n    Records are structured data entries that capture specific measurements, states, or observations\n    at precise points in time. Currently supports sleep records with plans to expand to other\n    record types in the future.\n\n    **Key Features:**\n    - **Bulk Creation**: Create multiple records in a single request\n    - **Precise Timing**: Support for start and end timestamps for duration-based records\n    - **Duplicate Detection**: Prevents creation of identical records\n    - **Metadata Tracking**: Automatic capture of data source and device information\n    - **Type Safety**: Structured validation for each record type\n\n    **Supported Record Types:**\n    - **Sleep Records**: Track sleep stages, duration, and quality metrics\n      - Required: type, timestamp, end_time, stage\n      - Stages: light, deep, rem, awake\n      - Duration calculated automatically from timestamp to end_time\n\n    **Request Body:**\n    - `documents`: Array of record objects to create (minimum 1 required)\n      - `type`: Record type (currently \"sleep_record\", required)\n      - `timestamp`: ISO 8601 datetime when the record period started (required)\n      - `end_time`: ISO 8601 datetime when the record period ended (required)\n      - `stage`: Sleep stage or record-specific state value (required)\n    - `metadata`: Source information for data provenance\n      - `origin`: Data entry method (\"manual\", \"automatic\", \"import\")\n      - `origin_device`: Device type (\"mobile_app\", \"wearable\", \"api\")\n      - `source_os`: Operating system (\"iOS\", \"Android\", \"Web\")\n      - `source_service`: Service identifier (default: \"LLIF\")\n\n    **Use Cases:**\n    - Sleep tracking and analysis\n    - Health monitoring with precise timing\n    - Automated data import from wearables\n    - Manual logging of structured observations", "operationId": "insert_record_endpoint_v3record__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsertRecordAPIRequestInput", "description": "Record data to insert"}, "example": {"documents": [{"type": "sleep_record", "timestamp": "2024-01-15T22:30:00Z", "end_time": "2024-01-16T06:30:00Z", "stage": "deep"}], "metadata": {"origin": "manual", "origin_device": "mobile_app", "source_os": "iOS", "source_service": "LLIF"}}}}}, "responses": {"200": {"description": "Records successfully created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_SleepRecordAPIOutput_"}, "examples": [{"summary": "Sleep record creation", "value": {"documents": [{"id": "123e4567-e89b-12d3-a456-************", "type": "sleep_record", "timestamp": "2024-01-15T22:30:00Z", "end_time": "2024-01-16T06:30:00Z", "stage": "deep", "created_at": "2024-01-16T10:00:00Z", "metadata": {"origin": "manual", "origin_device": "mobile_app", "source_os": "iOS", "source_service": "LLIF", "service": "DATA"}}]}}]}}}, "404": {"description": "Not found"}, "400": {"description": "Bad request - duplicate records found or invalid data", "content": {"application/json": {"examples": [{"summary": "Duplicate records", "value": {"detail": "record duplicates found in the input payload"}}, {"summary": "Invalid record data", "value": {"detail": "Invalid record data: end_time must be after timestamp"}}]}}}, "401": {"description": "Authentication required"}, "422": {"description": "Validation error in request body"}}}, "delete": {"tags": ["record"], "summary": "Delete Records", "description": "Delete existing records from the system permanently by their IDs and types.\n\n    This endpoint allows users to permanently remove one or more records from their personal\n    data collection. Once deleted, records cannot be recovered. Users must provide both the\n    record ID and type to identify which records to delete for security verification.\n\n    **Key Features:**\n    - **Permanent Deletion**: Records are completely removed and cannot be recovered\n    - **Bulk Operations**: Delete multiple records in a single request\n    - **Type Verification**: Record type must match existing record for security\n    - **Ownership Protection**: Users can only delete their own records\n    - **Atomic Operations**: All deletions succeed or fail together\n\n    **Security Measures:**\n    - Record type verification prevents accidental deletions\n    - User ownership validation ensures data isolation\n    - Atomic operations ensure all deletions succeed or fail together\n\n    **Request Body:**\n    - `documents`: Array of record deletion objects (minimum 1 required)\n      - `id`: UUID of the existing record to delete (required)\n      - `type`: Record type (must match existing record type for verification, required)\n\n    **Supported Record Types:**\n    - **sleep_record**: Sleep tracking records with stage and timing data\n\n    **Important Considerations:**\n    - **Irreversible**: Deletion is permanent and cannot be undone\n    - **Data Integrity**: Related analytics and aggregations may be affected\n    - **Audit Trail**: Deletion events may be logged for compliance purposes\n    - **Batch Size**: Large deletion requests may be processed asynchronously\n\n    **Use Cases:**\n    - Remove incorrect or duplicate sleep records\n    - Clean up test data or erroneous entries\n    - Comply with data retention policies\n    - Correct data import errors", "operationId": "delete_record_endpoint_v3record__delete", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteRecordAPIRequestInput", "description": "Record deletion request containing record IDs and types"}, "example": {"documents": [{"id": "123e4567-e89b-12d3-a456-************", "type": "sleep_record"}, {"id": "456e7890-e12b-34d5-a678-901234567890", "type": "sleep_record"}]}}}}, "responses": {"200": {"description": "Records successfully deleted", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsIdsResponse"}, "examples": [{"summary": "Successful deletion", "value": {"document_ids": ["123e4567-e89b-12d3-a456-************", "456e7890-e12b-34d5-a678-901234567890"]}}]}}}, "404": {"description": "One or more records not found"}, "400": {"description": "Bad request - invalid operation or record not found", "content": {"application/json": {"examples": [{"summary": "Record not found", "value": {"detail": "Record not found or cannot be deleted"}}, {"summary": "Type mismatch", "value": {"detail": "Record type does not match existing record"}}]}}}, "401": {"description": "Authentication required"}, "422": {"description": "Validation error in request body"}}}}, "/v3/analyse/trend_detect/": {"post": {"tags": ["analyse"], "summary": "Detect Trends in Time Series Data", "description": "Analyze time series data to detect statistically significant trends using linear regression.\n\n    This endpoint processes a sequence of numerical values to determine if there's a statistically\n    significant upward or downward trend. It uses linear regression analysis with configurable\n    thresholds for R-squared (model fit quality) and relative slope (trend significance).\n\n    **Key Features:**\n    - **Linear Regression Analysis**: Uses statistical methods to identify trends\n    - **Configurable Thresholds**: Adjustable sensitivity for trend detection\n    - **Statistical Validation**: R-squared and relative slope validation\n    - **Trend Classification**: Clear categorization of trend direction\n    - **Quantitative Results**: Detailed statistical metrics for analysis\n\n    **Algorithm Process:**\n    1. Applies linear regression to the input data series\n    2. Calculates R-squared to assess model fit quality\n    3. Computes relative slope (coefficient / mean) for trend significance\n    4. Classifies trend based on thresholds and statistical significance\n    5. Returns comprehensive statistical metrics\n\n    **Use Cases:**\n    - **Health Monitoring**: Analyze weight, blood pressure, or symptom trends over time\n    - **Symptom Tracking**: Detect patterns in symptom severity or frequency\n    - **Environmental Analysis**: Identify trends in environmental data\n    - **Performance Metrics**: Track improvement or decline in various measurements\n    - **Data Quality**: Validate data consistency and identify anomalies\n\n    **Request Parameters:**\n    - `data_series`: Array of numerical values (minimum 2 values required)\n    - `r2_threshold`: Minimum R-squared for significant trend (default: 0.3, range: 0.0-1.0)\n    - `relative_slope_threshold`: Minimum absolute relative slope (default: 0.01)\n\n    **Response Fields:**\n    - `trend_result`: \"UPWARD_TREND\", \"DOWNWARD_TREND\", or \"NO_TREND\"\n    - `coefficient`: Linear regression slope coefficient\n    - `intercept`: Linear regression y-intercept\n    - `relative_slope`: Normalized slope strength (coefficient / mean)", "operationId": "trend_detection_endpoint_v3_analyse_trend_detect__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrendDetectionAPIRequestInput"}}}}, "responses": {"200": {"description": "Trend analysis completed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrendDetectionAPIOutput"}, "example": {"trend_result": "UPWARD_TREND", "coefficient": 0.25, "intercept": 0.95, "relative_slope": 0.167}}}}, "404": {"description": "Not found"}, "400": {"description": "Invalid input data or parameters", "content": {"application/json": {"examples": [{"summary": "Insufficient data points", "value": {"detail": "Data series must contain at least 2 numerical values"}}, {"summary": "Invalid threshold values", "value": {"detail": "R-squared threshold must be between 0.0 and 1.0"}}]}}}, "422": {"description": "Validation error in request body"}}}}, "/v3/analyse/correlate/event/": {"post": {"tags": ["analyse"], "summary": "Analyze Event Correlations", "description": "Perform statistical correlation analysis between two event variables with temporal constraints.\n\n    This endpoint analyzes the statistical relationship between dependent and independent variables\n    from user events or environmental data. It automatically selects the appropriate correlation\n    method based on data types and provides comprehensive statistical analysis with temporal matching.\n\n    **Key Features:**\n    - **Multi-Method Analysis**: Automatically selects appropriate statistical methods\n      - Pearson correlation for continuous variables\n      - ANOVA F-statistic for continuous vs discrete/binary variables\n      - Chi-squared for discrete/binary vs discrete/binary variables\n    - **Temporal Matching**: Configurable time windows for event relationships\n    - **Data Source Support**: User events and environmental data (weather, air quality, pollen)\n    - **Field-Specific Analysis**: Analyze specific fields with aggregation methods\n    - **Bias Handling**: Selection bias correction for occurrence-based correlations\n    - **Statistical Validation**: P-values, confidence intervals, and significance testing\n\n    **Temporal Relationship Types:**\n    - **\"before\"**: Independent variable occurs before dependent variable\n    - **\"after\"**: Independent variable occurs after dependent variable\n    - **\"closest\"**: Matches closest temporal occurrence regardless of order\n\n    **Supported Data Sources:**\n    - **Event Data**: User-generated events (symptoms, medications, activities, etc.)\n    - **Environmental Data**: Weather conditions, air quality, pollen counts\n    - **Mixed Analysis**: Correlate events with environmental factors\n\n    **Request Structure:**\n    - `dependent`: Variable to be predicted/explained\n    - `independent`: Variable used for prediction/explanation\n    - `temporal_options`: Time-based matching configuration\n\n    **Response Components:**\n    - `data`: Value pairs used in correlation analysis\n    - `correlation`: Statistical results with coefficient, p-value, and significance\n    - `metadata`: Document counts and analysis details\n    - `suggested_visualisation`: Recommended chart type for results", "operationId": "event_correlation_endpoint_v3_analyse_correlate_event__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EventCorrelationAPIRequestInput"}}}}, "responses": {"200": {"description": "Correlation analysis completed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EventCorrelationAPIOutput"}, "example": {"data": [[7.5, 1], [8.2, 0], [6.1, 1], [9.0, 0]], "correlation": {"coefficient": -0.65, "p_value": 0.023, "certainty": "moderate", "relationship_strength": "strong"}, "dependent": {"document_count": 45}, "independent": {"document_count": 23}, "suggested_visualisation": "box_plot"}}}}, "404": {"description": "Not found"}, "400": {"description": "Invalid query parameters or temporal options", "content": {"application/json": {"examples": [{"summary": "Invalid query structure", "value": {"detail": "Invalid event query: missing required field 'types'"}}, {"summary": "Invalid temporal options", "value": {"detail": "Temporal interval must be positive and in valid format (e.g., '4h', '2d')"}}]}}}, "204": {"description": "No matching data found for correlation analysis", "content": {"application/json": {"example": {"detail": "No data available for correlation analysis with the specified parameters"}}}}, "401": {"description": "Authentication required"}, "422": {"description": "Validation error in request body"}}}}, "/v3/analyse/correlate/event/suggest/parameters": {"post": {"tags": ["analyse"], "summary": "Get AI-Powered Correlation Parameter Suggestions", "description": "Receive intelligent recommendations for optimal correlation analysis parameters using AI.\n\n    This endpoint leverages artificial intelligence to analyze provided event queries and suggest\n    the most appropriate parameters for running correlation analysis. The AI considers event types,\n    their typical relationships, domain knowledge, and best practices to recommend optimal settings\n    for meaningful statistical analysis.\n\n    **Key Features:**\n    - **Intelligent Analysis**: AI examines event types and schemas to understand data characteristics\n    - **Domain Knowledge**: Incorporates understanding of typical relationships between event categories\n    - **Optimal Parameters**: Suggests appropriate aggregation methods, temporal windows, and field selections\n    - **Reasoning Provided**: Detailed explanations for all recommendations\n    - **Learning Tool**: Helps users understand correlation analysis best practices\n\n    **AI Analysis Process:**\n    1. **Schema Analysis**: Examines event types and available fields\n    2. **Relationship Modeling**: Considers typical temporal relationships between event categories\n    3. **Method Selection**: Suggests appropriate aggregation methods based on data characteristics\n    4. **Temporal Optimization**: Recommends optimal time windows for meaningful correlations\n    5. **Validation**: Ensures suggested parameters will produce statistically valid results\n\n    **Use Cases:**\n    - **Getting Started**: Ideal for users new to correlation analysis\n    - **Parameter Validation**: Verify manual parameter choices against AI recommendations\n    - **Learning**: Understand typical relationships between different event types\n    - **Automation**: Streamline correlation setup for common analysis patterns\n    - **Best Practices**: Learn optimal parameter selection for various scenarios\n\n    **Request Structure:**\n    - `dependent_query`: Query defining the dependent variable events\n    - `independent_query`: Query defining the independent variable events\n\n    **Response Components:**\n    - `dependent`/`independent`: Suggested configurations with field names and aggregation methods\n    - `temporal_options`: Recommended temporal matching settings\n    - `reasoning`: Comprehensive explanation of all suggestions", "operationId": "suggest_correlation_parameters_endpoint_v3_analyse_correlate_event_suggest_parameters_post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuggestCorrelationParametersAPIRequestInput"}}}}, "responses": {"200": {"description": "Parameter suggestions generated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuggestCorrelationParametersAPIOutput"}, "example": {"dependent": {"field_name": "rating", "aggregation_method": "mean", "query": {"types": ["symptom"], "filters": {"name": "headache"}}}, "independent": {"query": {"types": ["food"], "filters": {"category": "caffeine"}}}, "temporal_options": {"type": "before", "time_input": {"interval": "4h", "time_gte": "2024-01-01", "time_lte": "2024-01-31"}}, "reasoning": "Suggesting 'before' with '4h' interval as caffeine intake often precedes headache onset within hours. Using 'rating' field for headache severity provides more nuanced analysis than simple occurrence counting."}}}}, "404": {"description": "Not found"}, "204": {"description": "AI could not generate suggestions for the provided queries", "content": {"application/json": {"example": {"detail": "Unable to generate correlation parameter suggestions for the specified event types"}}}}, "400": {"description": "Invalid query structure or unsupported event types", "content": {"application/json": {"example": {"detail": "Invalid event query: unsupported event type or malformed query structure"}}}}, "401": {"description": "Authentication required"}, "422": {"description": "Validation error in request body"}}}}, "/v3/template/search/": {"post": {"tags": ["template"], "summary": "Search Templates", "description": "Search and retrieve templates with advanced filtering, sorting, and pagination capabilities.\n\n    This endpoint allows you to find templates based on various criteria including name, type,\n    tags, and other template properties. Results are returned in a paginated format with\n    configurable sorting options.\n\n    **Key Features:**\n    - **Advanced Filtering**: Query by name, template type, tags, archived status, and more\n    - **Flexible Sorting**: Sort by any template field (created_at, name, updated_at, etc.)\n    - **Pagination**: Use limit and continuation_token for efficient data retrieval\n    - **Boolean Queries**: Combine multiple filters with AND/OR logic\n    - **Template Types**: Search across both event templates and group templates\n\n    **Template Types:**\n    - **Event Templates**: Contain structured event data for creating standardized events\n    - **Group Templates**: Collections of related event templates for complex workflows\n\n    **Common Use Cases:**\n    - Find templates by name or partial name match\n    - Filter templates by specific tags or categories\n    - Retrieve recently created or modified templates\n    - Search for templates of a specific type (event vs group)\n    - Find non-archived templates for active use\n\n    **Pagination:**\n    Use the `limit` parameter to control page size (1-10000, default: 100).\n    Use `continuation_token` from previous responses to fetch subsequent pages.", "operationId": "search_templates_endpoint_v3_template_search__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 10000, "exclusiveMinimum": 0, "default": 100, "title": "Limit"}}, {"name": "continuation_token", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Continuation Token"}}, {"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchTemplatesRequestInput"}}}}, "responses": {"200": {"description": "Templates found and returned successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_Union_EventTemplateAPIOutput__GroupTemplateAPIOutput__"}, "example": {"documents": [{"id": "123e4567-e89b-12d3-a456-************", "name": "Morning Exercise Template", "tags": ["exercise", "morning", "routine"], "document_type": "exercise", "document_name": "Morning Workout", "created_at": "2024-01-15T08:00:00Z"}]}}}}, "404": {"description": "Not found"}, "204": {"description": "No templates found matching the search criteria"}, "400": {"description": "Invalid search parameters or malformed request"}, "401": {"description": "Authentication required"}, "422": {"description": "Validation error in request parameters"}}}}, "/v3/template/": {"post": {"tags": ["template"], "summary": "Create Templates", "description": "Create new templates in the system for standardizing event creation and workflows.\n\n    This endpoint allows you to create both event templates and group templates that can be used\n    to streamline data entry and ensure consistency across your personal data collection.\n\n    **Template Types:**\n\n    **Event Templates:**\n    - Contain pre-configured event data with standardized fields\n    - Include event type, name, and structured document payload\n    - Can be used to quickly create consistent events\n    - Support all event types (exercise, nutrition, symptoms, medications, etc.)\n\n    **Group Templates:**\n    - Collections of related event templates for complex workflows\n    - Can reference existing templates by ID or include embedded templates\n    - Useful for multi-step routines or related event sequences\n    - Support both separate template references and inline template definitions\n\n    **Key Features:**\n    - **Duplicate Detection**: Prevents creation of templates with identical details\n    - **Validation**: Ensures all required fields are present and properly formatted\n    - **Tagging**: Support for custom tags to organize and categorize templates\n    - **Flexible Structure**: Accommodates various event types and data structures\n\n    **Common Use Cases:**\n    - Create exercise routine templates with pre-filled workout data\n    - Build medication templates with dosage and timing information\n    - Design symptom tracking templates with consistent rating scales\n    - Establish group templates for morning/evening routines\n\n    **Request Body:**\n    The request should contain a `documents` array with template definitions. Each template\n    must include a name, optional tags, and type-specific data (document for event templates,\n    template_ids or templates for group templates).", "operationId": "insert_template_endpoint_v3_template__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsertTemplateAPIRequestInput"}}}}, "responses": {"200": {"description": "Templates successfully created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_Union_EventTemplateAPIOutput__GroupTemplateAPIOutput__"}, "example": {"documents": [{"id": "123e4567-e89b-12d3-a456-************", "name": "Morning Exercise Template", "tags": ["exercise", "morning", "routine"], "document_type": "exercise", "document_name": "Morning Workout", "document": {"type": "exercise", "name": "Morning Workout", "duration_minutes": 30, "intensity": "moderate"}, "created_at": "2024-01-15T08:00:00Z"}]}}}}, "404": {"description": "Not found"}, "400": {"description": "Bad request - duplicate templates or validation errors", "content": {"application/json": {"examples": [{"summary": "Duplicate template", "value": {"detail": "Duplicate templates detected. A template with the same details already exists in the system."}}, {"summary": "Validation error", "value": {"detail": "Invalid template data: missing required fields"}}]}}}, "401": {"description": "Authentication required"}, "422": {"description": "Validation error in request body"}}}, "patch": {"tags": ["template"], "summary": "Update Templates", "description": "Update existing templates with new data, maintaining their identity and relationships.\n\n    This endpoint allows you to modify existing templates while preserving their unique identifiers\n    and system metadata. You can update template names, tags, document content, and other properties\n    for both event templates and group templates.\n\n    **Update Capabilities:**\n    - **Template Properties**: Modify name, tags, and other metadata\n    - **Event Template Content**: Update document payload, event type, and structure\n    - **Group Template Structure**: Modify template references and embedded templates\n    - **Partial Updates**: Only specified fields are updated, others remain unchanged\n    - **Batch Operations**: Update multiple templates in a single request\n\n    **Key Features:**\n    - **Identity Preservation**: Template IDs remain unchanged during updates\n    - **Duplicate Detection**: Prevents updates that would create duplicate templates\n    - **Validation**: Ensures updated data meets all requirements and constraints\n    - **Atomic Operations**: All updates in a batch succeed or fail together\n    - **Relationship Integrity**: Maintains valid references in group templates\n\n    **Update Types:**\n\n    **Event Template Updates:**\n    - Modify event document structure and content\n    - Update template name and descriptive information\n    - Change tags and categorization\n    - Adjust event type and associated metadata\n\n    **Group Template Updates:**\n    - Add or remove template references\n    - Update embedded template definitions\n    - Modify group structure and organization\n    - Change template ordering and relationships\n\n    **Common Use Cases:**\n    - Refine template content based on usage patterns\n    - Update template names for better organization\n    - Modify event structures to capture additional data\n    - Reorganize group templates for improved workflows\n    - Update tags and metadata for better searchability\n\n    **Request Body:**\n    The request should contain a `documents` array with template updates. Each update\n    must include the template ID and the fields to be modified. Only provided fields\n    will be updated; omitted fields remain unchanged.\n\n    **Important Notes:**\n    - Template IDs must exist and belong to the authenticated user\n    - Updates that would create duplicates are rejected\n    - Group template references must point to valid, accessible templates\n    - All validation rules apply to updated content", "operationId": "update_template_endpoint_v3_template__patch", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTemplateAPIRequestInput"}}}}, "responses": {"200": {"description": "Templates successfully updated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_Union_EventTemplateAPIOutput__GroupTemplateAPIOutput__"}, "examples": [{"summary": "Template update success", "value": {"documents": [{"id": "123e4567-e89b-12d3-a456-************", "name": "Updated Morning Exercise Template", "tags": ["exercise", "morning", "routine", "updated"], "document_type": "exercise", "document_name": "Enhanced Morning Workout", "document": {"type": "exercise", "name": "Enhanced Morning Workout", "duration_minutes": 45, "intensity": "high"}, "created_at": "2024-01-15T08:00:00Z", "updated_at": "2024-01-16T09:30:00Z"}]}}]}}}, "404": {"description": "One or more template IDs not found"}, "400": {"description": "Bad request - duplicate templates, validation errors, or invalid references", "content": {"application/json": {"examples": [{"summary": "Duplicate template", "value": {"detail": "template duplicates found in the update payload"}}, {"summary": "Validation error", "value": {"detail": "Invalid template data: template ID not found"}}]}}}, "401": {"description": "Authentication required"}, "422": {"description": "Validation error in request body"}}}}, "/v3/template/archive/": {"patch": {"tags": ["template"], "summary": "Archive Templates", "description": "Archive templates to remove them from active use while preserving their data and history.\n\n    This endpoint allows you to archive one or more templates, marking them as inactive without\n    permanently deleting them. Archived templates are excluded from search results by default\n    but can still be retrieved if specifically requested, and their data remains intact for\n    historical reference.\n\n    **Archive Behavior:**\n    - **Soft Deletion**: Templates are marked as archived, not permanently deleted\n    - **Data Preservation**: All template data, metadata, and relationships are preserved\n    - **Search Exclusion**: Archived templates are excluded from default search results\n    - **Reversible Operation**: Archived templates can potentially be restored (implementation dependent)\n    - **Batch Processing**: Multiple templates can be archived in a single operation\n\n    **Key Features:**\n    - **Identity Preservation**: Template IDs and all metadata remain unchanged\n    - **Timestamp Recording**: Archive operation is timestamped for audit purposes\n    - **Relationship Integrity**: Group template references to archived templates are handled gracefully\n    - **Atomic Operations**: All templates in the request are archived together or the operation fails\n    - **Validation**: Ensures all specified template IDs exist and belong to the user\n\n    **Use Cases:**\n    - **Template Lifecycle Management**: Remove outdated or unused templates from active use\n    - **Seasonal Templates**: Archive templates that are only relevant at certain times\n    - **Experimental Templates**: Archive test templates after evaluation\n    - **Cleanup Operations**: Organize template library by removing obsolete items\n    - **Workflow Optimization**: Reduce clutter in template selection interfaces\n\n    **Archive Effects:**\n    - Templates are marked with an `archived_at` timestamp\n    - Default searches will not return archived templates\n    - Group templates referencing archived templates may need attention\n    - Template usage statistics and history are preserved\n    - API responses will include the archive timestamp\n\n    **Query Parameters:**\n    - `template_ids`: Array of UUID strings identifying templates to archive\n    - Multiple IDs can be provided to archive templates in batch\n    - All specified templates must exist and belong to the authenticated user\n\n    **Important Notes:**\n    - Archiving is typically irreversible through this API (restoration may require separate operations)\n    - Group templates containing references to archived templates should be reviewed\n    - Archived templates may still appear in historical data and analytics\n    - Consider the impact on existing workflows before archiving frequently-used templates", "operationId": "archive_templates_endpoint_v3_template_archive__patch", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "template_ids", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string", "format": "uuid"}, "title": "Template Ids"}}, {"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "responses": {"200": {"description": "Templates successfully archived", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_Union_EventTemplateAPIOutput__GroupTemplateAPIOutput__"}, "examples": [{"summary": "Template archive success", "value": {"documents": [{"id": "123e4567-e89b-12d3-a456-************", "name": "Morning Exercise Template", "tags": ["exercise", "morning", "routine"], "document_type": "exercise", "document_name": "Morning Workout", "archived_at": "2024-01-16T10:30:00Z", "created_at": "2024-01-15T08:00:00Z", "updated_at": "2024-01-16T09:30:00Z"}]}}]}}}, "404": {"description": "One or more template IDs not found"}, "400": {"description": "Bad request - invalid template IDs or operation not allowed", "content": {"application/json": {"examples": [{"summary": "Invalid template IDs", "value": {"detail": "One or more template IDs are invalid or do not belong to the user"}}]}}}, "401": {"description": "Authentication required"}, "422": {"description": "Validation error in query parameters"}}}}, "/v3/x/lookup/": {"post": {"tags": ["lookup"], "summary": "Extract content metadata from URL", "description": "Extract structured metadata from web content using URL lookup.\n\n    This endpoint analyzes a provided URL and extracts key metadata including title, content type,\n    and description. It supports various content types including articles, videos, websites,\n    and interactive content. The service attempts to parse standard web metadata (Open Graph,\n    Twitter Cards, etc.) and categorizes the content appropriately.\n\n    **Key Features:**\n    - **Intelligent Parsing**: Extracts metadata from various web content formats\n    - **Content Classification**: Automatically categorizes content by type\n    - **Metadata Extraction**: Pulls title, description, and other relevant information\n    - **Error Resilience**: Graceful handling of inaccessible or malformed content\n    - **Multiple Protocols**: Supports HTTP, HTTPS, and various content delivery networks\n\n    **Supported Content Types:**\n    - **Articles**: Blog posts, news articles, documentation, and written content\n    - **Videos**: YouTube, Vimeo, and other video platforms with rich metadata\n    - **Websites**: General web pages, landing pages, and corporate sites\n    - **Interactive**: Web applications, tools, and interactive content platforms\n    - **Audio**: Podcasts, music platforms, and audio content\n    - **Images**: Image galleries, photos, and visual content platforms\n\n    **Content Analysis Features:**\n    - **Automatic Detection**: Identifies content type based on URL patterns and metadata\n    - **Title Extraction**: Pulls titles from page metadata, Open Graph, or content analysis\n    - **Description Parsing**: Extracts descriptions from meta tags, summaries, or content\n    - **Fallback Mechanisms**: Uses multiple strategies when primary metadata is unavailable\n\n    **Metadata Sources:**\n    - **Open Graph**: Facebook's Open Graph protocol metadata\n    - **Twitter Cards**: Twitter's metadata format for rich content previews\n    - **Schema.org**: Structured data markup for enhanced content understanding\n    - **HTML Meta Tags**: Standard HTML meta description and title tags\n    - **Content Analysis**: Direct content parsing when metadata is insufficient\n\n    **Use Cases:**\n    - Link preview generation for social media or messaging applications\n    - Content curation and bookmark management\n    - Research and reference collection\n    - Automated content categorization\n    - Rich link sharing in collaborative platforms", "operationId": "post_content_lookup_v3_x_lookup__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostContentLookupRequestInput", "description": "URL to analyze and extract content metadata from", "examples": [{"summary": "News article URL", "description": "Extract metadata from a news article", "value": {"url": "https://example.com/news/article-title"}}, {"summary": "Video content URL", "description": "Extract metadata from video content", "value": {"url": "https://youtube.com/watch?v=dQw4w9WgXcQ"}}, {"summary": "General website URL", "description": "Extract metadata from a general website", "value": {"url": "https://github.com/user/repository"}}]}}}}, "responses": {"200": {"description": "Content metadata extracted successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ContentLookupOutputBoundary"}, "examples": [{"summary": "Article metadata", "value": {"title": "How to Build Better APIs", "content_type": "article", "description": "A comprehensive guide to designing and implementing robust APIs for modern applications."}}, {"summary": "Video metadata", "value": {"title": "Introduction to Machine Learning", "content_type": "video", "description": "Learn the fundamentals of machine learning in this beginner-friendly tutorial."}}, {"summary": "Website metadata", "value": {"title": "GitHub - user/repository", "content_type": "interactive", "description": "Open source project for building scalable web applications."}}]}}}, "404": {"description": "Not found"}, "204": {"description": "No content could be extracted from the URL", "content": {"application/json": {"examples": [{"summary": "URL inaccessible"}]}}}, "400": {"description": "Bad request - invalid URL format or malformed request", "content": {"application/json": {"examples": [{"summary": "Invalid URL format", "value": {"detail": "Invalid URL format provided"}}, {"summary": "Missing URL", "value": {"detail": "URL is required in request body"}}]}}}, "401": {"description": "Authentication required"}, "422": {"description": "Validation error in request body"}, "500": {"description": "Internal server error - content lookup service unavailable", "content": {"application/json": {"examples": [{"summary": "Service error", "value": {"detail": "Content lookup service is temporarily unavailable"}}]}}}}}}, "/v3/plan/search/": {"post": {"tags": ["plan"], "summary": "Search Plans", "description": "Search and retrieve plans with advanced filtering, sorting, and pagination capabilities.\n\n    This endpoint allows you to find plans based on various criteria including name, template,\n    scheduling properties, and custom tags. Results are returned in a paginated format with\n    configurable sorting options.\n\n    **Key Features:**\n    - **Advanced Filtering**: Query by name, template_id, urgency, completion status, and more\n    - **Flexible Sorting**: Sort by any plan field (created_at, next_scheduled_at, priority, etc.)\n    - **Pagination**: Use limit and continuation_token for efficient data retrieval\n    - **Boolean Queries**: Combine multiple filters with AND/OR logic\n\n    **Common Use Cases:**\n    - Find all urgent plans: `{\"query\": {\"filters\": {\"is_urgent\": true}}}`\n    - Search by name: `{\"query\": {\"filters\": {\"name\": {\"contains\": \"workout\"}}}}`\n    - Get overdue plans: `{\"query\": {\"filters\": {\"next_scheduled_at\": {\"lt\": \"2024-01-15T00:00:00Z\"}}}}`\n    - Find plans by template: `{\"query\": {\"filters\": {\"template_id\": \"uuid-here\"}}}`\n\n    **Sorting Options:**\n    - `system_properties.created_at` (default)\n    - `next_scheduled_at`\n    - `priority`\n    - `name`\n    - Any other plan field\n\n    **Response includes:**\n    - Plan metadata (name, template_id, scheduling info)\n    - Completion tracking (current_completed, max_completed, streak)\n    - System properties (id, created_at, updated_at)\n    - Custom tags and notes", "operationId": "search_plans_endpoint_v3_plan_search__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 10000, "exclusiveMinimum": 0, "default": 100, "title": "Limit"}}, {"name": "continuation_token", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Continuation Token"}}, {"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchPlansRequestInput"}}}}, "responses": {"200": {"description": "List of plans matching the search criteria, sorted according to the specified order", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_PlanAPIOutput_"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/plan/archive/": {"patch": {"tags": ["plan"], "summary": "Archive Plans", "description": "Archive multiple plans by their IDs, marking them as inactive while preserving their data.\n\n    This endpoint allows you to archive one or more plans simultaneously. Archived plans are\n    marked with an `archived_at` timestamp and are typically excluded from active plan queries.\n    The operation is reversible - archived plans can be reactivated by updating their archived_at\n    field to null.\n\n    **Key Features:**\n    - **Bulk Operation**: Archive multiple plans in a single request\n    - **Soft Delete**: Plans are marked as archived, not permanently deleted\n    - **Ownership Validation**: Only plans owned by the authenticated user can be archived\n    - **Atomic Operation**: All plans are archived together or the operation fails\n\n    **Use Cases:**\n    - Clean up completed or obsolete plans\n    - Temporarily hide plans from active views\n    - Bulk management of seasonal or project-specific plans\n\n    **Query Parameters:**\n    - `plan_ids`: Array of UUID strings identifying the plans to archive\n\n    **Response:**\n    - Returns the archived plans with updated `archived_at` timestamps\n    - All other plan properties remain unchanged\n\n    **HTTP Status Codes:**\n    - 200: Plans successfully archived\n    - 400: Invalid plan IDs or operation not allowed (e.g., plans not owned by user)\n    - 401: Authentication required\n    - 404: One or more plans not found", "operationId": "archive_plans_endpoint_v3_plan_archive__patch", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "plan_ids", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string", "format": "uuid"}, "description": "List of plan IDs to archive", "title": "Plan Ids"}, "description": "List of plan IDs to archive"}, {"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "responses": {"200": {"description": "List of archived plans with updated archived_at timestamps", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_PlanAPIOutput_"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/plan/": {"patch": {"tags": ["plan"], "summary": "Update Plans", "description": "Update existing plans with new properties and scheduling information.\n\n    This endpoint allows you to modify one or more existing plans. All plan properties can be\n    updated including scheduling, completion tracking, metadata, and recurrence rules. The\n    operation validates that updated plans maintain consistency and proper scheduling logic.\n\n    **Key Features:**\n    - **Bulk Updates**: Modify multiple plans in a single request\n    - **Full Property Support**: Update any plan field including complex scheduling rules\n    - **Validation**: Ensures updated plans have valid recurrence patterns and completion logic\n    - **Ownership Protection**: Only plans owned by the authenticated user can be updated\n\n    **Updatable Properties:**\n    - **Basic Info**: name, template_id, note, tags, priority\n    - **Scheduling**: next_scheduled_at, recurrence, is_absolute_schedule\n    - **Behavior**: is_urgent, is_confirmation_required\n    - **Progress**: current_completed, max_completed, streak\n\n    **Validation Rules:**\n    - If `recurrence` is null, `max_completed - current_completed` must equal 1\n    - `recurrence` must be valid RRULE format with DTSTART\n    - `next_scheduled_at` must be after recurrence DTSTART\n    - `current_completed` cannot exceed `max_completed`\n\n    **Request Body:**\n    ```json\n    {\n      \"documents\": [{\n        \"id\": \"plan-uuid\",\n        \"type\": \"plan\",\n        \"name\": \"Updated plan name\",\n        \"next_scheduled_at\": \"2024-01-15T09:00:00Z\",\n        \"current_completed\": 5,\n        \"is_urgent\": true\n      }]\n    }\n    ```\n\n    **HTTP Status Codes:**\n    - 200: Plans successfully updated\n    - 400: Invalid data, duplicate plans in payload, or validation errors\n    - 401: Authentication required\n    - 404: One or more plans not found\n    - 422: Request body validation error", "operationId": "update_plans_endpoint_v3_plan__patch", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePlansAPIRequestInput"}}}}, "responses": {"200": {"description": "List of updated plans with modified properties and updated system timestamps", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_PlanAPIOutput_"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["plan"], "summary": "Create Plans", "description": "Create new plans with scheduling, completion tracking, and recurrence capabilities.\n\n    This endpoint allows you to create one or more new plans. Plans are structured tasks or goals\n    that can be scheduled to occur once or repeatedly based on recurrence rules. Each plan is\n    linked to a template and can track completion progress with streak counting.\n\n    **Key Features:**\n    - **Bulk Creation**: Create multiple plans in a single request\n    - **Template Integration**: Plans must reference existing templates\n    - **Flexible Scheduling**: Support for one-time and recurring plans\n    - **Progress Tracking**: Built-in completion counting and streak tracking\n    - **Duplicate Detection**: Prevents creation of identical plans\n\n    **Required Properties:**\n    - **name**: Descriptive name for the plan (1-200 characters)\n    - **template_id**: UUID of the associated template\n    - **prompt**: Action prompt or instruction text\n    - **type**: Must be \"plan\"\n\n    **Optional Properties:**\n    - **recurrence**: RRULE format string for recurring plans\n    - **next_scheduled_at**: When the plan should next be executed\n    - **is_urgent**: Priority flag for urgent plans\n    - **is_confirmation_required**: Whether completion needs confirmation\n    - **is_absolute_schedule**: Whether timing is flexible or fixed\n    - **priority**: Plan priority level (unset, low, medium, high)\n    - **current_completed**: Starting completion count (default: 0)\n    - **max_completed**: Maximum completions allowed\n    - **note**: Additional notes or context\n    - **tags**: Array of custom tags for categorization\n\n    **Request Body Example:**\n    ```json\n    {\n      \"documents\": [{\n        \"type\": \"plan\",\n        \"name\": \"Daily Morning Workout\",\n        \"template_id\": \"template-uuid\",\n        \"prompt\": \"Complete 30-minute morning exercise routine\",\n        \"recurrence\": \"DTSTART:20240115T070000Z\\nRRULE:FREQ=DAILY\",\n        \"is_urgent\": false,\n        \"is_confirmation_required\": true,\n        \"priority\": \"medium\",\n        \"tags\": [\"fitness\", \"morning\", \"routine\"]\n      }]\n    }\n    ```\n\n    **HTTP Status Codes:**\n    - 200: Plans successfully created\n    - 400: Invalid data or duplicate plans detected\n    - 401: Authentication required\n    - 422: Request body validation error", "operationId": "insert_plans_endpoint_v3_plan__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsertPlansAPIRequestInput"}}}}, "responses": {"200": {"description": "List of created plans with generated IDs and system properties", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_PlanAPIOutput_"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/v3/plan/complete/": {"post": {"tags": ["plan"], "summary": "Complete Plans", "description": "Mark plans as completed and generate associated completion events.\n\n    This endpoint processes plan completions by updating completion counters, managing streaks,\n    calculating next scheduled times for recurring plans, and creating completion events that\n    are added to the user's timeline. It's the primary way to record plan execution.\n\n    **Key Features:**\n    - **Completion Processing**: Updates completion counts and streak tracking\n    - **Event Generation**: Creates timeline events documenting the completion\n    - **Recurrence Handling**: Automatically schedules next occurrence for recurring plans\n    - **Streak Management**: Maintains and updates completion streaks\n    - **Bulk Operations**: Complete multiple plans in a single request\n\n    **Completion Logic:**\n    - Increments `current_completed` counter\n    - Updates streak information (current streak, longest streak, total triggered)\n    - For recurring plans: calculates and sets `next_scheduled_at`\n    - For one-time plans: may mark as fully completed if max_completed is reached\n    - Sets `first_completed_at` timestamp if this is the first completion\n\n    **Generated Events:**\n    - Each completion creates an event in the user's timeline\n    - Events include plan metadata and completion context\n    - Events are linked to the originating plan for traceability\n\n    **Request Body:**\n    ```json\n    {\n      \"documents\": [{\n        \"plan_id\": \"plan-uuid\",\n        \"completed_at\": \"2024-01-15T09:30:00Z\",\n        \"notes\": \"Completed morning workout routine\",\n        \"rating\": 8\n      }]\n    }\n    ```\n\n    **Response Structure:**\n    - Returns completed plans with updated completion data\n    - Includes all generated events for each completion\n    - Events contain full event data (timestamp, type, metadata)\n\n    **HTTP Status Codes:**\n    - 200: Plans successfully completed and events generated\n    - 400: Invalid completion data or operation not allowed\n    - 401: Authentication required\n    - 404: One or more plans not found\n    - 422: Request body validation error\n\n    **Use Cases:**\n    - Record daily habit completions\n    - Log workout or medication adherence\n    - Track goal progress with automatic scheduling\n    - Generate completion events for analytics", "operationId": "complete_plans_endpoint_v3_plan_complete__post", "security": [{"CustomHTTPBearer": []}], "parameters": [{"name": "api_refresh_token", "in": "cookie", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Api Refresh <PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompletePlansAPIRequestInput"}}}}, "responses": {"200": {"description": "List of completed plans with their generated completion events", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CommonDocumentsResponse_CompletePlanAPIOutput_"}}}}, "404": {"description": "Not found"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}}, "components": {"schemas": {"ActivityType": {"type": "string", "enum": ["walking", "on_foot", "running", "cycling", "boating", "on_bicycle", "skiing", "in_vehicle", "in_road_vehicle", "in_four_wheeler_vehicle", "motorcycling", "in_passenger_vehicle", "in_bus", "in_rail_vehicle", "in_train", "in_subway", "in_tram", "sailing", "in_ferry", "flying", "still", "unknown_activity_type"], "title": "ActivityType"}, "Administration": {"type": "string", "enum": ["capsule", "tablet", "oral", "injection", "patch", "suppository", "topical", "drops", "inhaler", "spray", "other"], "title": "Administration"}, "AggregationMethod": {"type": "string", "enum": ["stats", "sum", "min", "max", "avg"], "title": "AggregationMethod"}, "AnalysisCertainty": {"type": "string", "enum": ["strong evidence", "moderate evidence", "weak evidence", "insufficient evidence"], "title": "AnalysisCertainty"}, "AnalysisRelationshipLabel": {"type": "string", "enum": ["no relationship", "potential relationship", "very weak relationship", "weak relationship", "moderate relationship", "strong relationship"], "title": "AnalysisRelationshipLabel"}, "Application": {"type": "string", "enum": ["apple_health_kit", "llif_mobile", "my_life_log"], "title": "Application"}, "AssetReference": {"properties": {"asset_type": {"$ref": "#/components/schemas/AssetType"}, "asset_id": {"type": "string", "pattern": "^[A-Za-z0-9._-]+$", "title": "Asset Id", "default": "asset_id"}}, "type": "object", "required": ["asset_type"], "title": "AssetReference"}, "AssetReferenceModel": {"properties": {"asset_type": {"$ref": "#/components/schemas/AssetType"}, "asset_id": {"type": "string", "pattern": "^[A-Za-z0-9._-]+$", "title": "Asset Id"}}, "type": "object", "required": ["asset_type", "asset_id"], "title": "AssetReferenceModel"}, "AssetType": {"type": "string", "enum": ["image", "pdf"], "title": "AssetType"}, "AudioAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "audio", "title": "Type"}, "category": {"$ref": "#/components/schemas/AudioCategory"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "title", "type", "category", "duration"], "title": "AudioAPIOutput"}, "AudioCategory": {"type": "string", "enum": ["audio", "podcast", "audiobook", "music"], "title": "AudioCategory"}, "AudioTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "audio", "title": "Type"}, "category": {"$ref": "#/components/schemas/AudioCategory"}}, "type": "object", "required": ["name", "duration", "note", "title", "url", "rating", "type", "category"], "title": "AudioTemplatePayload"}, "BloodGlucoseAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "blood_glucose", "title": "Type"}, "category": {"$ref": "#/components/schemas/BloodGlucoseCategory", "default": "blood_glucose"}, "value": {"type": "number", "maximum": 4000.0, "minimum": 0.0, "title": "Value"}, "specimen_source": {"$ref": "#/components/schemas/BloodGlucoseSpecimenSource", "default": "unknown"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "value", "duration"], "title": "BloodGlucoseAPIOutput"}, "BloodGlucoseCategory": {"type": "string", "enum": ["blood_glucose"], "title": "BloodGlucoseCategory"}, "BloodGlucoseSpecimenSource": {"type": "string", "enum": ["interstitial_fluid", "capillary_blood", "plasma", "serum", "tears", "whole_blood", "unknown"], "title": "BloodGlucoseSpecimenSource"}, "BloodGlucoseTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "blood_glucose", "title": "Type"}, "category": {"$ref": "#/components/schemas/BloodGlucoseCategory"}, "value": {"type": "number", "maximum": 4000.0, "minimum": 0.0, "title": "Value"}, "specimen_source": {"$ref": "#/components/schemas/BloodGlucoseSpecimenSource"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "value", "specimen_source"], "title": "BloodGlucoseTemplatePayload"}, "BloodPressureAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "blood_pressure", "title": "Type"}, "category": {"$ref": "#/components/schemas/BloodPressureCategory"}, "diastolic": {"type": "number", "maximum": 500.0, "minimum": 0.0, "title": "Diastolic"}, "systolic": {"type": "number", "maximum": 500.0, "minimum": 0.0, "title": "Systolic"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "diastolic", "systolic", "duration"], "title": "BloodPressureAPIOutput"}, "BloodPressureCategory": {"type": "string", "enum": ["blood_pressure"], "title": "BloodPressureCategory"}, "BloodPressureTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "blood_pressure", "title": "Type"}, "category": {"$ref": "#/components/schemas/BloodPressureCategory"}, "diastolic": {"type": "number", "maximum": 500.0, "minimum": 0.0, "title": "Diastolic"}, "systolic": {"type": "number", "maximum": 500.0, "minimum": 0.0, "title": "Systolic"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "diastolic", "systolic"], "title": "BloodPressureTemplatePayload"}, "BodyMetricAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "body_metric", "title": "Type"}, "category": {"$ref": "#/components/schemas/BodyMetricCategory"}, "value": {"type": "number", "maximum": 1000000000.0, "minimum": -1000000000.0, "title": "Value"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "value", "duration"], "title": "BodyMetricAPIOutput"}, "BodyMetricCategory": {"type": "string", "enum": ["body_metric", "body_fat", "body_temperature", "pulse_oxygen", "respiratory_rate", "weight"], "title": "BodyMetricCategory"}, "BodyMetricTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "body_metric", "title": "Type"}, "category": {"$ref": "#/components/schemas/BodyMetricCategory"}, "value": {"type": "number", "maximum": 1000000000.0, "minimum": -1000000000.0, "title": "Value"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "value"], "title": "BodyMetricTemplatePayload"}, "BodyParts": {"type": "string", "enum": ["right.front.head.forehead", "left.front.head.forehead", "right.front.head.face", "left.front.head.face", "right.front.neck", "left.front.neck", "right.front.torso.shoulder", "right.front.torso.chest", "left.front.torso.chest", "left.front.torso.shoulder", "right.front.torso.shoulder.arm.upper", "left.front.torso.shoulder.arm.upper", "right.front.torso.shoulder.arm.elbow", "left.front.torso.shoulder.arm.elbow", "right.front.torso.shoulder.arm.forearm", "right.front.torso.abdomen", "left.front.torso.abdomen", "left.front.torso.shoulder.arm.forearm", "right.front.torso.shoulder.arm.wrist", "right.front.pelvis.hip", "right.front.pelvis.genital", "left.front.pelvis.genital", "left.front.pelvis.hip", "left.front.torso.shoulder.arm.wrist", "right.front.torso.shoulder.arm.hand", "right.front.leg.upper", "left.front.leg.upper", "left.front.torso.shoulder.arm.hand", "right.front.leg.knee", "left.front.leg.knee", "right.front.leg.lower", "left.front.leg.lower", "right.front.leg.ankle", "left.front.leg.ankle", "right.front.leg.foot", "left.front.leg.foot", "left.back.head.top", "right.back.head.top", "left.back.head", "right.back.head", "left.back.neck", "right.back.neck", "left.back.torso.shoulder", "left.back.torso.upper", "right.back.torso.upper", "right.back.torso.shoulder", "left.back.torso.shoulder.arm.upper", "left.back.torso.middle", "right.back.torso.middle", "right.back.torso.shoulder.arm.upper", "left.back.torso.shoulder.arm.elbow", "right.back.torso.shoulder.arm.elbow", "left.back.torso.shoulder.arm.forearm", "left.back.torso.lower", "right.back.torso.lower", "right.back.torso.shoulder.arm.forearm", "left.back.torso.shoulder.arm.wrist", "left.back.pelvis.hip", "left.back.pelvis.glute", "right.back.pelvis.glute", "right.back.pelvis.hip", "right.back.torso.shoulder.arm.wrist", "left.back.torso.shoulder.arm.hand", "left.back.leg.upper", "right.back.leg.upper", "right.back.torso.shoulder.arm.hand", "left.back.leg.knee", "right.back.leg.knee", "left.back.leg.lower", "right.back.leg.lower", "left.back.leg.ankle", "right.back.leg.ankle", "left.back.leg.foot", "right.back.leg.foot"], "title": "BodyParts", "description": "Body parts as described in the CHOIR body map"}, "BooleanQueryType": {"type": "string", "enum": ["and", "or", "not"], "title": "BooleanQueryType"}, "BucketAggregationMethod": {"type": "string", "enum": ["derivative", "moving_avg", "cumulative_sum"], "title": "BucketAggregationMethod"}, "CalendarAggregationType": {"type": "string", "enum": ["month_names", "hours_in_day", "parts_of_month", "days_of_month", "weekdays", "lunar_phases", "time_between"], "title": "CalendarAggregationType"}, "CalendarFrequencyDistributionAPIOutput": {"properties": {"results": {"items": {"$ref": "#/components/schemas/FrequencyDistributionAggregate"}, "type": "array", "title": "Results"}}, "type": "object", "required": ["results"], "title": "CalendarFrequencyDistributionAPIOutput"}, "CalendarFrequencyDistributionAPIRequestInput": {"properties": {"queries": {"items": {"$ref": "#/components/schemas/EventTypedQueryAPI-Input"}, "type": "array", "title": "Queries"}, "calendar_aggregation_type": {"$ref": "#/components/schemas/CalendarAggregationType"}, "fill_null_values": {"type": "boolean", "title": "Fill Null Values", "default": true}}, "type": "object", "required": ["calendar_aggregation_type"], "title": "CalendarFrequencyDistributionAPIRequestInput"}, "CalendarHistogramAggregate": {"properties": {"aggregation_key": {"type": "string", "title": "Aggregation Key"}, "agg_method": {"type": "string", "title": "Agg Method"}, "doc_count": {"type": "integer", "title": "Doc Count"}, "value": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Value"}}, "type": "object", "required": ["aggregation_key", "agg_method", "doc_count", "value"], "title": "CalendarHistogramAggregate"}, "CalendarHistogramAggregationAPIOutput": {"properties": {"results": {"items": {"$ref": "#/components/schemas/CalendarHistogramAggregate"}, "type": "array", "title": "Results"}}, "type": "object", "required": ["results"], "title": "CalendarHistogramAggregationAPIOutput"}, "CalendarHistogramAggregationAPIRequestInput": {"properties": {"queries": {"items": {"$ref": "#/components/schemas/EventTypedQueryAPI-Input"}, "type": "array", "title": "Queries"}, "calendar_aggregation_type": {"$ref": "#/components/schemas/CalendarAggregationType"}, "fill_null_values": {"type": "boolean", "title": "Fill Null Values", "default": true}, "field_name": {"type": "string", "title": "Field Name"}, "aggregation_method": {"$ref": "#/components/schemas/SimpleAggregationMethod"}}, "type": "object", "required": ["calendar_aggregation_type", "field_name", "aggregation_method"], "title": "CalendarHistogramAggregationAPIRequestInput"}, "CardioAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "cardio", "title": "Type"}, "category": {"$ref": "#/components/schemas/CardioCategory"}, "distance": {"anyOf": [{"type": "number", "ge": 0, "le": 100000}, {"type": "null"}], "title": "Distance"}, "elevation": {"anyOf": [{"type": "number", "ge": -500, "le": 8848}, {"type": "null"}], "title": "Elevation"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "distance", "elevation", "rating", "duration"], "title": "CardioAPIOutput"}, "CardioCategory": {"type": "string", "enum": ["run", "walk", "hike", "dance", "jump_rope", "row", "elliptical", "stair_climb", "swim", "cycling", "cardio"], "title": "CardioCategory"}, "CardioTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "cardio", "title": "Type"}, "category": {"$ref": "#/components/schemas/CardioCategory"}, "distance": {"anyOf": [{"type": "number", "ge": 0, "le": 100000}, {"type": "null"}], "title": "Distance"}, "elevation": {"anyOf": [{"type": "number", "ge": -500, "le": 8848}, {"type": "null"}], "title": "Elevation"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "distance", "elevation", "rating"], "title": "CardioTemplatePayload"}, "CommonDocumentsIdsResponse": {"properties": {"document_ids": {"items": {"type": "string", "format": "uuid"}, "type": "array", "minItems": 1, "title": "Document Ids"}}, "type": "object", "required": ["document_ids"], "title": "CommonDocumentsIdsResponse"}, "CommonDocumentsResponse_Annotated_Union_BloodGlucoseAPIOutput__BloodPressureAPIOutput__BodyMetricAPIOutput__AudioAPIOutput__ContentAPIOutput__ImageAPIOutput__InteractiveAPIOutput__TextAPIOutput__VideoAPIOutput__ExerciseAPIOutput__CardioAPIOutput__StrengthAPIOutput__EmotionAPIOutput__StressAPIOutput__DrinkAPIOutput__FoodAPIOutput__SupplementAPIOutput__CoreEventAPIOutput__SleepV3APIOutput__EventGroupAPIOutput__NoteAPIOutput__SymptomAPIOutput__MedicationAPIOutput___FieldInfo_annotation_NoneType__required_True__discriminator__type____": {"properties": {"documents": {"items": {"oneOf": [{"$ref": "#/components/schemas/BloodGlucoseAPIOutput"}, {"$ref": "#/components/schemas/BloodPressureAPIOutput"}, {"$ref": "#/components/schemas/BodyMetricAPIOutput"}, {"$ref": "#/components/schemas/AudioAPIOutput"}, {"$ref": "#/components/schemas/ContentAPIOutput"}, {"$ref": "#/components/schemas/ImageAPIOutput"}, {"$ref": "#/components/schemas/InteractiveAPIOutput"}, {"$ref": "#/components/schemas/TextAPIOutput"}, {"$ref": "#/components/schemas/VideoAPIOutput"}, {"$ref": "#/components/schemas/ExerciseAPIOutput"}, {"$ref": "#/components/schemas/CardioAPIOutput"}, {"$ref": "#/components/schemas/StrengthAPIOutput"}, {"$ref": "#/components/schemas/EmotionAPIOutput"}, {"$ref": "#/components/schemas/StressAPIOutput"}, {"$ref": "#/components/schemas/DrinkAPIOutput"}, {"$ref": "#/components/schemas/FoodAPIOutput"}, {"$ref": "#/components/schemas/SupplementAPIOutput"}, {"$ref": "#/components/schemas/CoreEventAPIOutput"}, {"$ref": "#/components/schemas/SleepV3APIOutput"}, {"$ref": "#/components/schemas/EventGroupAPIOutput"}, {"$ref": "#/components/schemas/NoteAPIOutput"}, {"$ref": "#/components/schemas/SymptomAPIOutput"}, {"$ref": "#/components/schemas/MedicationAPIOutput"}], "discriminator": {"propertyName": "type", "mapping": {"audio": "#/components/schemas/AudioAPIOutput", "blood_glucose": "#/components/schemas/BloodGlucoseAPIOutput", "blood_pressure": "#/components/schemas/BloodPressureAPIOutput", "body_metric": "#/components/schemas/BodyMetricAPIOutput", "cardio": "#/components/schemas/CardioAPIOutput", "content": "#/components/schemas/ContentAPIOutput", "core_event": "#/components/schemas/CoreEventAPIOutput", "drink": "#/components/schemas/DrinkAPIOutput", "emotion": "#/components/schemas/EmotionAPIOutput", "event_group": "#/components/schemas/EventGroupAPIOutput", "exercise": "#/components/schemas/ExerciseAPIOutput", "food": "#/components/schemas/FoodAPIOutput", "image": "#/components/schemas/ImageAPIOutput", "interactive": "#/components/schemas/InteractiveAPIOutput", "medication": "#/components/schemas/MedicationAPIOutput", "note": "#/components/schemas/NoteAPIOutput", "sleep": "#/components/schemas/SleepV3APIOutput", "strength": "#/components/schemas/StrengthAPIOutput", "stress": "#/components/schemas/StressAPIOutput", "supplement": "#/components/schemas/SupplementAPIOutput", "symptom": "#/components/schemas/SymptomAPIOutput", "text": "#/components/schemas/TextAPIOutput", "video": "#/components/schemas/VideoAPIOutput"}}}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "CommonDocumentsResponse[Annotated[Union[BloodGlucoseAPIOutput, BloodPressureAPIOutput, BodyMetricAPIOutput, AudioAPIOutput, ContentAPIOutput, ImageAPIOutput, InteractiveAPIOutput, TextAPIOutput, VideoAPIOutput, ExerciseAPIOutput, CardioAPIOutput, StrengthAPIOutput, EmotionAPIOutput, StressAPIOutput, DrinkAPIOutput, FoodAPIOutput, SupplementAPIOutput, CoreEventAPIOutput, SleepV3APIOutput, EventGroupAPIOutput, NoteAPIOutput, SymptomAPIOutput, MedicationAPIOutput], FieldInfo(annotation=NoneType, required=True, discriminator='type')]]"}, "CommonDocumentsResponse_CompletePlanAPIOutput_": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/CompletePlanAPIOutput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "CommonDocumentsResponse[CompletePlanAPIOutput]"}, "CommonDocumentsResponse_PlanAPIOutput_": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/PlanAPIOutput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "CommonDocumentsResponse[PlanAPIOutput]"}, "CommonDocumentsResponse_SleepRecordAPIOutput_": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/SleepRecordAPIOutput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "CommonDocumentsResponse[SleepRecordAPIOutput]"}, "CommonDocumentsResponse_Union_EventTemplateAPIOutput__GroupTemplateAPIOutput__": {"properties": {"documents": {"items": {"anyOf": [{"$ref": "#/components/schemas/EventTemplateAPIOutput"}, {"$ref": "#/components/schemas/GroupTemplateAPIOutput"}]}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "CommonDocumentsResponse[Union[EventTemplateAPIOutput, GroupTemplateAPIOutput]]"}, "CommonDocumentsResponse_UseCaseAPIOutput_": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/UseCaseAPIOutput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "CommonDocumentsResponse[UseCaseAPIOutput]"}, "CompletePlanAPIOutput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "type": {"type": "string", "const": "plan", "title": "Type"}, "metadata": {"$ref": "#/components/schemas/PlanMetadata"}, "name": {"type": "string", "maxLength": 64, "minLength": 1, "title": "Name"}, "template_id": {"type": "string", "format": "uuid", "title": "Template Id"}, "next_scheduled_at": {"type": "string", "format": "date-time", "title": "Next Scheduled At"}, "first_completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "First Completed At"}, "archived_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Archived At"}, "is_urgent": {"type": "boolean", "title": "<PERSON>"}, "is_confirmation_required": {"type": "boolean", "title": "Is Confirmation Required"}, "is_absolute_schedule": {"type": "boolean", "title": "Is Absolute Schedule"}, "streak": {"$ref": "#/components/schemas/PlanStreak"}, "recurrence": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Recurrence"}, "note": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Note"}, "priority": {"$ref": "#/components/schemas/Priority"}, "prompt": {"type": "string", "minLength": 1, "title": "Prompt"}, "current_completed": {"type": "integer", "minimum": 0.0, "title": "Current Completed"}, "max_completed": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Max Completed"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "events": {"items": {"oneOf": [{"$ref": "#/components/schemas/BloodGlucoseAPIOutput"}, {"$ref": "#/components/schemas/BloodPressureAPIOutput"}, {"$ref": "#/components/schemas/BodyMetricAPIOutput"}, {"$ref": "#/components/schemas/AudioAPIOutput"}, {"$ref": "#/components/schemas/ContentAPIOutput"}, {"$ref": "#/components/schemas/ImageAPIOutput"}, {"$ref": "#/components/schemas/InteractiveAPIOutput"}, {"$ref": "#/components/schemas/TextAPIOutput"}, {"$ref": "#/components/schemas/VideoAPIOutput"}, {"$ref": "#/components/schemas/ExerciseAPIOutput"}, {"$ref": "#/components/schemas/CardioAPIOutput"}, {"$ref": "#/components/schemas/StrengthAPIOutput"}, {"$ref": "#/components/schemas/EmotionAPIOutput"}, {"$ref": "#/components/schemas/StressAPIOutput"}, {"$ref": "#/components/schemas/DrinkAPIOutput"}, {"$ref": "#/components/schemas/FoodAPIOutput"}, {"$ref": "#/components/schemas/SupplementAPIOutput"}, {"$ref": "#/components/schemas/CoreEventAPIOutput"}, {"$ref": "#/components/schemas/SleepV3APIOutput"}, {"$ref": "#/components/schemas/EventGroupAPIOutput"}, {"$ref": "#/components/schemas/NoteAPIOutput"}, {"$ref": "#/components/schemas/SymptomAPIOutput"}, {"$ref": "#/components/schemas/MedicationAPIOutput"}], "discriminator": {"propertyName": "type", "mapping": {"audio": "#/components/schemas/AudioAPIOutput", "blood_glucose": "#/components/schemas/BloodGlucoseAPIOutput", "blood_pressure": "#/components/schemas/BloodPressureAPIOutput", "body_metric": "#/components/schemas/BodyMetricAPIOutput", "cardio": "#/components/schemas/CardioAPIOutput", "content": "#/components/schemas/ContentAPIOutput", "core_event": "#/components/schemas/CoreEventAPIOutput", "drink": "#/components/schemas/DrinkAPIOutput", "emotion": "#/components/schemas/EmotionAPIOutput", "event_group": "#/components/schemas/EventGroupAPIOutput", "exercise": "#/components/schemas/ExerciseAPIOutput", "food": "#/components/schemas/FoodAPIOutput", "image": "#/components/schemas/ImageAPIOutput", "interactive": "#/components/schemas/InteractiveAPIOutput", "medication": "#/components/schemas/MedicationAPIOutput", "note": "#/components/schemas/NoteAPIOutput", "sleep": "#/components/schemas/SleepV3APIOutput", "strength": "#/components/schemas/StrengthAPIOutput", "stress": "#/components/schemas/StressAPIOutput", "supplement": "#/components/schemas/SupplementAPIOutput", "symptom": "#/components/schemas/SymptomAPIOutput", "text": "#/components/schemas/TextAPIOutput", "video": "#/components/schemas/VideoAPIOutput"}}}, "type": "array", "minItems": 1, "title": "Events"}}, "type": "object", "required": ["id", "system_properties", "type", "metadata", "name", "template_id", "next_scheduled_at", "first_completed_at", "archived_at", "is_urgent", "is_confirmation_required", "is_absolute_schedule", "streak", "recurrence", "note", "priority", "prompt", "current_completed", "max_completed", "tags", "events"], "title": "CompletePlanAPIOutput"}, "CompletePlanInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "completed_at": {"type": "string", "format": "date-time", "title": "Completed At"}, "payload": {"anyOf": [{"oneOf": [{"$ref": "#/components/schemas/AudioTemplatePayload"}, {"$ref": "#/components/schemas/ContentTemplatePayload"}, {"$ref": "#/components/schemas/ImageTemplatePayload"}, {"$ref": "#/components/schemas/InteractiveTemplatePayload"}, {"$ref": "#/components/schemas/TextTemplatePayload"}, {"$ref": "#/components/schemas/VideoTemplatePayload"}, {"$ref": "#/components/schemas/BloodGlucoseTemplatePayload"}, {"$ref": "#/components/schemas/BloodPressureTemplatePayload"}, {"$ref": "#/components/schemas/BodyMetricTemplatePayload"}, {"$ref": "#/components/schemas/ExerciseTemplatePayload"}, {"$ref": "#/components/schemas/CardioTemplatePayload"}, {"$ref": "#/components/schemas/StrengthTemplatePayload"}, {"$ref": "#/components/schemas/EmotionTemplatePayload"}, {"$ref": "#/components/schemas/StressTemplatePayload"}, {"$ref": "#/components/schemas/DrinkTemplatePayload"}, {"$ref": "#/components/schemas/FoodTemplatePayload"}, {"$ref": "#/components/schemas/SupplementTemplatePayload"}, {"$ref": "#/components/schemas/CoreEventTemplatePayload"}, {"$ref": "#/components/schemas/NoteTemplatePayload"}, {"$ref": "#/components/schemas/SymptomTemplatePayload"}, {"$ref": "#/components/schemas/MedicationTemplatePayload-Input"}, {"$ref": "#/components/schemas/SleepV3TemplatePayload"}], "discriminator": {"propertyName": "type", "mapping": {"audio": "#/components/schemas/AudioTemplatePayload", "blood_glucose": "#/components/schemas/BloodGlucoseTemplatePayload", "blood_pressure": "#/components/schemas/BloodPressureTemplatePayload", "body_metric": "#/components/schemas/BodyMetricTemplatePayload", "cardio": "#/components/schemas/CardioTemplatePayload", "content": "#/components/schemas/ContentTemplatePayload", "core_event": "#/components/schemas/CoreEventTemplatePayload", "drink": "#/components/schemas/DrinkTemplatePayload", "emotion": "#/components/schemas/EmotionTemplatePayload", "exercise": "#/components/schemas/ExerciseTemplatePayload", "food": "#/components/schemas/FoodTemplatePayload", "image": "#/components/schemas/ImageTemplatePayload", "interactive": "#/components/schemas/InteractiveTemplatePayload", "medication": "#/components/schemas/MedicationTemplatePayload-Input", "note": "#/components/schemas/NoteTemplatePayload", "sleep": "#/components/schemas/SleepV3TemplatePayload", "strength": "#/components/schemas/StrengthTemplatePayload", "stress": "#/components/schemas/StressTemplatePayload", "supplement": "#/components/schemas/SupplementTemplatePayload", "symptom": "#/components/schemas/SymptomTemplatePayload", "text": "#/components/schemas/TextTemplatePayload", "video": "#/components/schemas/VideoTemplatePayload"}}}, {"type": "null"}], "title": "Payload"}}, "type": "object", "required": ["id"], "title": "CompletePlanInput"}, "CompletePlansAPIRequestInput": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/CompletePlanInput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "CompletePlansAPIRequestInput"}, "CompoundBooleanQueryAPI-Input": {"properties": {"type": {"$ref": "#/components/schemas/BooleanQueryType"}, "queries": {"items": {"anyOf": [{"oneOf": [{"$ref": "#/components/schemas/ValuesQueryAPI"}, {"$ref": "#/components/schemas/PatternQueryAPI"}, {"$ref": "#/components/schemas/RangeQueryAPI"}, {"$ref": "#/components/schemas/ExistsQueryAPI"}, {"$ref": "#/components/schemas/RadiusQueryAPI"}], "discriminator": {"propertyName": "type", "mapping": {"exists": "#/components/schemas/ExistsQueryAPI", "pattern": "#/components/schemas/PatternQueryAPI", "radius": "#/components/schemas/RadiusQueryAPI", "range": "#/components/schemas/RangeQueryAPI", "values": "#/components/schemas/ValuesQueryAPI"}}}, {"$ref": "#/components/schemas/CompoundBooleanQueryAPI-Input"}]}, "type": "array", "minItems": 1, "title": "Queries"}}, "type": "object", "required": ["type", "queries"], "title": "CompoundBooleanQueryAPI"}, "CompoundBooleanQueryAPI-Output": {"properties": {"type": {"$ref": "#/components/schemas/BooleanQueryType"}, "queries": {"items": {"anyOf": [{"oneOf": [{"$ref": "#/components/schemas/ValuesQueryAPI"}, {"$ref": "#/components/schemas/PatternQueryAPI"}, {"$ref": "#/components/schemas/RangeQueryAPI"}, {"$ref": "#/components/schemas/ExistsQueryAPI"}, {"$ref": "#/components/schemas/RadiusQueryAPI"}], "discriminator": {"propertyName": "type", "mapping": {"exists": "#/components/schemas/ExistsQueryAPI", "pattern": "#/components/schemas/PatternQueryAPI", "radius": "#/components/schemas/RadiusQueryAPI", "range": "#/components/schemas/RangeQueryAPI", "values": "#/components/schemas/ValuesQueryAPI"}}}, {"$ref": "#/components/schemas/CompoundBooleanQueryAPI-Output"}]}, "type": "array", "minItems": 1, "title": "Queries"}}, "type": "object", "required": ["type", "queries"], "title": "CompoundBooleanQueryAPI"}, "ConsumeUnit": {"type": "string", "enum": ["doses", "items"], "title": "ConsumeUnit"}, "ConsumedNutritionType": {"type": "string", "enum": ["unit", "serving", "item"], "title": "ConsumedNutritionType"}, "ContentAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "content", "title": "Type"}, "category": {"$ref": "#/components/schemas/ContentCategory"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "title", "type", "category", "duration"], "title": "ContentAPIOutput"}, "ContentCategory": {"type": "string", "enum": ["content", "sport", "social_media", "news"], "title": "ContentCategory"}, "ContentLookupOutputBoundary": {"properties": {"title": {"type": "string", "title": "Title", "description": "title of the webpage as provided by the url"}, "type": {"anyOf": [{"$ref": "#/components/schemas/ContentCategory"}, {"$ref": "#/components/schemas/AudioCategory"}, {"$ref": "#/components/schemas/VideoCategory"}, {"$ref": "#/components/schemas/TextCategory"}, {"$ref": "#/components/schemas/ImageCategory"}, {"$ref": "#/components/schemas/InteractiveCategory"}], "title": "Type", "description": "type of the content, e.g. article, video, website"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "description provided by the website about the url"}}, "type": "object", "required": ["title", "type", "description"], "title": "ContentLookupOutputBoundary"}, "ContentTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "content", "title": "Type"}, "category": {"$ref": "#/components/schemas/ContentCategory"}}, "type": "object", "required": ["name", "duration", "note", "title", "url", "rating", "type", "category"], "title": "ContentTemplatePayload"}, "CoordinatesModel": {"properties": {"lat": {"type": "number", "maximum": 90.0, "minimum": -90.0, "title": "Lat"}, "lon": {"type": "number", "maximum": 180.0, "minimum": -180.0, "title": "Lon"}}, "type": "object", "required": ["lat", "lon"], "title": "CoordinatesModel"}, "CoreEventAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "core_event", "title": "Type"}, "category": {"$ref": "#/components/schemas/CoreEventCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "rating", "duration"], "title": "CoreEventAPIOutput"}, "CoreEventCategory": {"type": "string", "enum": ["core_event", "idle_downtime", "idle_nap", "idle_wait", "idle_other", "intimacy_partner", "intimacy_self", "intimacy_spiritual", "intimacy_other", "service_beauty", "service_doctor", "service_education", "service_finance", "service_mental_health", "service_physical_health", "service_property", "service_veterinarian", "service_other", "social_assist", "social_group", "social_individual", "social_remote", "social_other", "task_baby_care", "task_clean", "task_create", "task_finance", "task_garden", "task_laundry", "task_lawn_maintenance", "task_meal", "task_meal_preparation", "task_parent", "task_pet_care", "task_plan", "task_home_care", "task_car_care", "task_study", "task_technology", "task_volunteer", "task_other", "travel_bike", "travel_boat", "travel_drive", "travel_fly", "travel_walk", "travel_other", "work_mentor", "work_network", "work_primary_work", "work_professional_development", "work_supplemental_work", "work_other", "rating_positive", "rating_negative", "rating_neutral"], "title": "CoreEventCategory"}, "CoreEventTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "core_event", "title": "Type"}, "category": {"$ref": "#/components/schemas/CoreEventCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "rating"], "title": "CoreEventTemplatePayload"}, "CorrelationTemporalOptions": {"properties": {"type": {"type": "string", "enum": ["before", "after", "closest"], "title": "Type"}, "time_input": {"$ref": "#/components/schemas/TimeInput"}}, "type": "object", "required": ["type", "time_input"], "title": "CorrelationTemporalOptions"}, "CorrelationVariableAggregate": {"properties": {"count": {"type": "integer", "minimum": 0.0, "title": "Count"}}, "type": "object", "required": ["count"], "title": "CorrelationVariableAggregate"}, "CorrelationVariableInput-Input": {"properties": {"field_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Field Name"}, "query": {"anyOf": [{"$ref": "#/components/schemas/EventTypedQueryAPI-Input"}, {"$ref": "#/components/schemas/EnvironmentTypedQuery"}], "title": "Query"}, "aggregation_method": {"anyOf": [{"$ref": "#/components/schemas/SimpleAggregationMethod"}, {"type": "null"}]}}, "type": "object", "required": ["field_name", "query", "aggregation_method"], "title": "CorrelationVariableInput"}, "CorrelationVariableInput-Output": {"properties": {"field_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Field Name"}, "query": {"anyOf": [{"$ref": "#/components/schemas/EventTypedQueryAPI-Output"}, {"$ref": "#/components/schemas/EnvironmentTypedQuery"}], "title": "Query"}, "aggregation_method": {"anyOf": [{"$ref": "#/components/schemas/SimpleAggregationMethod"}, {"type": "null"}]}}, "type": "object", "required": ["field_name", "query", "aggregation_method"], "title": "CorrelationVariableInput"}, "DataHistogramAPIOutput": {"properties": {"results": {"items": {"$ref": "#/components/schemas/DateHistogramAggregate"}, "type": "array", "title": "Results"}}, "type": "object", "required": ["results"], "title": "DataHistogramAPIOutput"}, "DataHistogramAPIRequestInput": {"properties": {"queries": {"items": {"$ref": "#/components/schemas/EventTypedQueryAPI-Input"}, "type": "array", "title": "Queries"}, "aggregation": {"$ref": "#/components/schemas/DateHistogramAggregation"}}, "type": "object", "required": ["aggregation"], "title": "DataHistogramAPIRequestInput"}, "DataIntegrity": {"type": "integer", "enum": [5, 4, 3, 2, 1, 0], "title": "DataIntegrity", "description": "Data integrity captures the quality of the data\n\nVERY HIGH: Cloud to cloud, or mobile to cloud\nHIGH: Data through intermediate proxy like AHK or Google Fit\nMEDIUM: File upload\nLOW: Manual entry\nVERY LOW: Interpolated"}, "DataQuality": {"type": "integer", "enum": [5, 4, 3, 2, 1], "title": "DataQuality", "description": "Curates the quality of the sensor data"}, "DateHistogramAggregate": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"type": "string", "format": "date-time", "title": "End Time"}, "doc_count": {"type": "integer", "title": "Doc Count"}, "aggregates": {"items": {"$ref": "#/components/schemas/DateHistogramFieldAggregate"}, "type": "array", "title": "Aggregates"}}, "type": "object", "required": ["timestamp", "end_time", "doc_count", "aggregates"], "title": "DateHistogramAggregate"}, "DateHistogramAggregation": {"properties": {"interval": {"type": "string", "minLength": 2, "pattern": "^(1[mhdwMqy]|\\d+(ms|s|m|h|d))$", "title": "Interval", "description": "Allowed intervals are NX where N is an integer and X equals ms, s, m, h, dOR 1X where X equals m, h, d, w, M, q, y"}, "default_aggregation_method": {"$ref": "#/components/schemas/AggregationMethod"}, "histogram_field_aggregations": {"items": {"$ref": "#/components/schemas/DateHistogramFieldAggregation"}, "type": "array", "title": "Histogram Field Aggregations"}, "timezone": {"type": "string", "format": "zoneinfo", "title": "Timezone", "default": "UTC"}}, "type": "object", "required": ["interval", "default_aggregation_method", "histogram_field_aggregations"], "title": "DateHistogramAggregation"}, "DateHistogramBucketFieldAggregation": {"properties": {"aggregation_method": {"$ref": "#/components/schemas/BucketAggregationMethod"}, "window": {"type": "integer", "maximum": 100.0, "minimum": 1.0, "title": "Window", "description": "window size for moving average", "default": 10}}, "type": "object", "required": ["aggregation_method"], "title": "DateHistogramBucketFieldAggregation"}, "DateHistogramFieldAggregate": {"properties": {"field": {"type": "string", "title": "Field"}, "agg_method": {"type": "string", "title": "Agg Method"}, "value": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Value"}}, "type": "object", "required": ["field", "agg_method", "value"], "title": "DateHistogramFieldAggregate"}, "DateHistogramFieldAggregation": {"properties": {"field_name": {"type": "string", "minLength": 1, "title": "Field Name"}, "aggregation_method": {"anyOf": [{"$ref": "#/components/schemas/AggregationMethod"}, {"type": "null"}]}, "bucket_aggregation": {"items": {"$ref": "#/components/schemas/DateHistogramBucketFieldAggregation"}, "type": "array", "title": "Bucket Aggregation"}}, "type": "object", "required": ["field_name"], "title": "DateHistogramFieldAggregation"}, "DeleteEventAPIRequestInput": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/DeleteEventInput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "DeleteEventAPIRequestInput"}, "DeleteEventInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "type": {"$ref": "#/components/schemas/EventV3Type"}}, "type": "object", "required": ["id", "type"], "title": "DeleteEventInput"}, "DeleteRecordAPIRequestInput": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/DeleteRecordInput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "DeleteRecordAPIRequestInput"}, "DeleteRecordInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "type": {"$ref": "#/components/schemas/RecordType"}}, "type": "object", "required": ["id", "type"], "title": "DeleteRecordInput"}, "DiaryEventAPIOutput": {"properties": {"tags": {"anyOf": [{"items": {"type": "string"}, "type": "array", "maxItems": 64, "minItems": 1}, {"type": "null"}], "title": "Tags"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "duration": {"anyOf": [{"type": "integer", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "type": {"$ref": "#/components/schemas/DiaryEventType"}, "name": {"type": "string", "maxLength": 32, "minLength": 1, "title": "Name"}, "is_standard": {"type": "boolean", "title": "Is Standard", "default": false}, "is_archived": {"type": "boolean", "title": "Is Archived", "default": false}, "custom_data": {"anyOf": [{"items": {"$ref": "#/components/schemas/DiaryEventsCustomDataItem"}, "type": "array"}, {"type": "null"}], "title": "Custom Data"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReferenceModel"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "explanation": {"anyOf": [{"type": "string", "maxLength": 4096, "minLength": 1}, {"type": "null"}], "title": "Explanation"}, "origin_url": {"anyOf": [{"type": "string", "maxLength": 2048, "minLength": 1}, {"type": "null"}], "title": "Origin Url"}, "intensity": {"anyOf": [{"type": "integer", "maximum": 100.0, "minimum": 0.0}, {"type": "null"}], "title": "Intensity"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/DiaryEventsPlanExtension"}, {"type": "null"}]}, "consumables_extension": {"anyOf": [{"$ref": "#/components/schemas/DiaryEventsConsumablesExtension"}, {"type": "null"}]}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchema"}, "_doc_id": {"type": "string", "format": "uuid", "title": "Doc Id"}, "metadata": {"$ref": "#/components/schemas/MetadataOutput"}}, "type": "object", "required": ["timestamp", "type", "name", "_doc_id", "metadata"], "title": "DiaryEventAPIOutput"}, "DiaryEventType": {"type": "string", "enum": ["drink", "event", "exercise", "food", "medication", "pain", "supplement", "symptom", "measurement", "task"], "title": "DiaryEventType"}, "DiaryEventsConsumablesExtension": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 128, "minLength": 1}, {"type": "null"}], "title": "Name"}, "units": {"anyOf": [{"type": "string", "maxLength": 32, "minLength": 1}, {"type": "null"}], "title": "Units"}, "quantity": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Quantity"}, "quantity_type": {"anyOf": [{"$ref": "#/components/schemas/ConsumedNutritionType"}, {"type": "null"}]}, "quantity_per_serving": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Quantity Per Serving"}, "amount": {"anyOf": [{"type": "number", "ge": 0, "le": 1000000}, {"type": "null"}], "title": "Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "fat": {"anyOf": [{"type": "number", "ge": 0, "le": 1000}, {"type": "null"}], "title": "Fat"}, "saturated_fat": {"anyOf": [{"type": "number", "ge": 0, "le": 300}, {"type": "null"}], "title": "Saturated Fat"}, "polyunsaturated_fat": {"anyOf": [{"type": "number", "ge": 0, "le": 300}, {"type": "null"}], "title": "Polyunsaturated Fat"}, "monounsaturated_fat": {"anyOf": [{"type": "number", "ge": 0, "le": 300}, {"type": "null"}], "title": "Monounsaturated Fat"}, "trans_fat": {"anyOf": [{"type": "number", "ge": 0, "le": 100}, {"type": "null"}], "title": "Trans Fat"}, "cholesterol": {"anyOf": [{"type": "number", "ge": 0, "le": 4}, {"type": "null"}], "title": "Cholesterol"}, "sodium": {"anyOf": [{"type": "number", "ge": 0, "le": 25}, {"type": "null"}], "title": "Sodium"}, "potassium": {"anyOf": [{"type": "number", "ge": 0, "le": 50}, {"type": "null"}], "title": "Potassium"}, "carbohydrates": {"anyOf": [{"type": "number", "ge": 0, "le": 3000}, {"type": "null"}], "title": "Carbohydrates"}, "fiber": {"anyOf": [{"type": "number", "ge": 0, "le": 250}, {"type": "null"}], "title": "Fiber"}, "sugar": {"anyOf": [{"type": "number", "ge": 0, "le": 600}, {"type": "null"}], "title": "Sugar"}, "protein": {"anyOf": [{"type": "number", "ge": 0, "le": 600}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "vitamin_a": {"anyOf": [{"type": "number", "ge": 0, "le": 0.015}, {"type": "null"}], "title": "Vitamin A"}, "vitamin_c": {"anyOf": [{"type": "number", "ge": 0, "le": 10}, {"type": "null"}], "title": "Vitamin C"}, "iron": {"anyOf": [{"type": "number", "ge": 0, "le": 0.2}, {"type": "null"}], "title": "Iron"}, "calcium": {"anyOf": [{"type": "number", "ge": 0, "le": 15}, {"type": "null"}], "title": "Calcium"}}, "type": "object", "title": "DiaryEventsConsumablesExtension"}, "DiaryEventsCustomDataItem": {"properties": {"key": {"type": "string", "minLength": 1, "title": "Key"}, "value": {"type": "number", "maximum": 1000000000000.0, "minimum": -1000000000000.0, "title": "Value"}}, "type": "object", "required": ["key", "value"], "title": "DiaryEventsCustomDataItem"}, "DiaryEventsPlanExtension": {"properties": {"plan_id": {"type": "string", "format": "uuid", "title": "Plan Id"}, "scheduled_at": {"type": "string", "format": "date-time", "title": "Scheduled At"}}, "type": "object", "required": ["plan_id", "scheduled_at"], "title": "DiaryEventsPlanExtension"}, "DocumentQueryAPI": {"properties": {"queries": {"items": {"$ref": "#/components/schemas/DocumentTypedQueryAPI"}, "type": "array", "title": "Queries"}}, "type": "object", "title": "DocumentQueryAPI"}, "DocumentType": {"type": "string", "enum": ["blood_glucose", "blood_pressure", "body_metric", "audio", "content", "image", "interactive", "text", "video", "cardio", "exercise", "strength", "emotion", "stress", "drink", "food", "supplement", "core_event", "sleep", "note", "symptom", "medication", "event_template", "group_template", "plan", "use_case", "event_group"], "title": "DocumentType"}, "DocumentTypedQueryAPI": {"properties": {"types": {"items": {"$ref": "#/components/schemas/DocumentType"}, "type": "array", "minItems": 1, "title": "Types"}, "query": {"anyOf": [{"oneOf": [{"$ref": "#/components/schemas/ValuesQueryAPI"}, {"$ref": "#/components/schemas/PatternQueryAPI"}, {"$ref": "#/components/schemas/RangeQueryAPI"}, {"$ref": "#/components/schemas/ExistsQueryAPI"}, {"$ref": "#/components/schemas/RadiusQueryAPI"}], "discriminator": {"propertyName": "type", "mapping": {"exists": "#/components/schemas/ExistsQueryAPI", "pattern": "#/components/schemas/PatternQueryAPI", "radius": "#/components/schemas/RadiusQueryAPI", "range": "#/components/schemas/RangeQueryAPI", "values": "#/components/schemas/ValuesQueryAPI"}}}, {"$ref": "#/components/schemas/CompoundBooleanQueryAPI-Input"}, {"type": "null"}], "title": "Query"}}, "type": "object", "required": ["types"], "title": "DocumentTypedQueryAPI"}, "DrinkAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "type": {"type": "string", "const": "drink", "title": "Type"}, "category": {"$ref": "#/components/schemas/DrinkCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "rating", "consumed_amount", "calories", "flavor", "type", "category", "consumed_type", "duration"], "title": "DrinkAPIOutput"}, "DrinkCategory": {"type": "string", "enum": ["tea", "milk", "wine", "beer", "water", "coffee", "smoothie", "spirits", "plant_based", "soft_drinks", "energy_drinks", "protein_shake", "juices_from_fruit", "juices_from_vegetable", "other"], "title": "DrinkCategory"}, "DrinkTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "items_per_serving": {"anyOf": [{"type": "number", "ge": 0, "le": 1000}, {"type": "null"}], "title": "Items Per Serving"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "amount_volume": {"anyOf": [{"type": "number", "ge": 0, "le": 1000}, {"type": "null"}], "title": "Amount Volume"}, "unit_volume": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit", "maxLength": 32}, {"type": "null"}]}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "type": {"type": "string", "const": "drink", "title": "Type"}, "category": {"$ref": "#/components/schemas/DrinkCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}}, "type": "object", "required": ["name", "duration", "note", "brand", "rating", "items_per_serving", "consumed_amount", "amount_volume", "unit_volume", "calories", "flavor", "nutrients", "type", "category", "consumed_type"], "title": "DrinkTemplatePayload"}, "EmotionAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "emotion", "title": "Type"}, "category": {"$ref": "#/components/schemas/EmotionCategory"}, "rating": {"type": "integer", "maximum": 10.0, "minimum": -5.0, "title": "Rating"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "rating", "duration"], "title": "EmotionAPIOutput"}, "EmotionCategory": {"type": "string", "enum": ["emotion", "joy", "sad", "fear", "disgust", "anger", "surprise", "anticipation", "trust", "anxiety", "mood"], "title": "EmotionCategory"}, "EmotionTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "emotion", "title": "Type"}, "category": {"$ref": "#/components/schemas/EmotionCategory"}, "rating": {"type": "integer", "maximum": 10.0, "minimum": 0.0, "title": "Rating"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "rating"], "title": "EmotionTemplatePayload"}, "EnvironmentTypedQuery": {"properties": {"domain_type": {"type": "string", "enum": ["AirQuality", "Weather", "<PERSON><PERSON>"], "title": "Domain Type"}}, "type": "object", "required": ["domain_type"], "title": "EnvironmentTypedQuery"}, "EventCorrelationAPIOutput": {"properties": {"data": {"items": {"prefixItems": [{"type": "number"}, {"type": "number"}], "type": "array", "maxItems": 2, "minItems": 2}, "type": "array", "title": "Data"}, "independent": {"$ref": "#/components/schemas/CorrelationVariableAggregate"}, "dependent": {"$ref": "#/components/schemas/CorrelationVariableAggregate"}, "correlation": {"$ref": "#/components/schemas/EventCorrelationAggregate"}, "suggested_visualisation": {"type": "string", "enum": ["box_plot", "scatter_plot", "heat_map"], "title": "Suggested Visualisation"}}, "type": "object", "required": ["data", "independent", "dependent", "correlation", "suggested_visualisation"], "title": "EventCorrelationAPIOutput"}, "EventCorrelationAPIRequestInput": {"properties": {"dependent": {"$ref": "#/components/schemas/CorrelationVariableInput-Input"}, "independent": {"$ref": "#/components/schemas/CorrelationVariableInput-Input"}, "temporal_options": {"$ref": "#/components/schemas/CorrelationTemporalOptions"}}, "type": "object", "required": ["dependent", "independent", "temporal_options"], "title": "EventCorrelationAPIRequestInput"}, "EventCorrelationAggregate": {"properties": {"correlation_coefficient": {"type": "number", "maximum": 1.0, "minimum": -1.0, "title": "Correlation Coefficient"}, "p_value": {"type": "number", "maximum": 1.0, "minimum": 0.0, "title": "P Value"}, "degree_of_freedom": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Degree Of Freedom"}, "covariance": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Covariance"}, "certainty": {"$ref": "#/components/schemas/AnalysisCertainty"}, "relationship": {"$ref": "#/components/schemas/AnalysisRelationshipLabel"}}, "type": "object", "required": ["correlation_coefficient", "p_value", "certainty", "relationship"], "title": "EventCorrelationAggregate"}, "EventFeedAPIRequestInput": {"properties": {"queries": {"items": {"$ref": "#/components/schemas/EventTypedQueryAPI-Input"}, "type": "array", "title": "Queries"}, "sort": {"$ref": "#/components/schemas/SortRequestInput", "default": {"field_name": "timestamp", "order": "descending"}}}, "type": "object", "title": "EventFeedAPIRequestInput"}, "EventFeedAPIResponse": {"properties": {"continuation_token": {"type": "string", "minLength": 1, "title": "Continuation Token"}, "items": {"items": {"anyOf": [{"$ref": "#/components/schemas/HeartRateAPIOutput"}, {"$ref": "#/components/schemas/RestingHeartRateAPIOutput"}, {"$ref": "#/components/schemas/SleepAPIOutput"}, {"$ref": "#/components/schemas/StepsAPIOutput"}, {"$ref": "#/components/schemas/DiaryEventAPIOutput"}, {"$ref": "#/components/schemas/LocationAPIOutput"}, {"$ref": "#/components/schemas/BloodGlucoseAPIOutput"}, {"$ref": "#/components/schemas/BloodPressureAPIOutput"}, {"$ref": "#/components/schemas/BodyMetricAPIOutput"}, {"$ref": "#/components/schemas/AudioAPIOutput"}, {"$ref": "#/components/schemas/ContentAPIOutput"}, {"$ref": "#/components/schemas/ImageAPIOutput"}, {"$ref": "#/components/schemas/InteractiveAPIOutput"}, {"$ref": "#/components/schemas/TextAPIOutput"}, {"$ref": "#/components/schemas/VideoAPIOutput"}, {"$ref": "#/components/schemas/ExerciseAPIOutput"}, {"$ref": "#/components/schemas/CardioAPIOutput"}, {"$ref": "#/components/schemas/StrengthAPIOutput"}, {"$ref": "#/components/schemas/EmotionAPIOutput"}, {"$ref": "#/components/schemas/StressAPIOutput"}, {"$ref": "#/components/schemas/DrinkAPIOutput"}, {"$ref": "#/components/schemas/FoodAPIOutput"}, {"$ref": "#/components/schemas/SupplementAPIOutput"}, {"$ref": "#/components/schemas/CoreEventAPIOutput"}, {"$ref": "#/components/schemas/SleepV3APIOutput"}, {"$ref": "#/components/schemas/EventGroupAPIOutput"}, {"$ref": "#/components/schemas/NoteAPIOutput"}, {"$ref": "#/components/schemas/SymptomAPIOutput"}, {"$ref": "#/components/schemas/MedicationAPIOutput"}]}, "type": "array", "minItems": 1, "title": "Items"}}, "type": "object", "required": ["continuation_token", "items"], "title": "EventFeedAPIResponse"}, "EventGroupAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "event_group", "title": "Type"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "duration"], "title": "EventGroupAPIOutput"}, "EventInputAsset": {"properties": {"payload": {"type": "string", "maxLength": 1000000, "minLength": 1, "format": "binary", "title": "Payload"}, "asset_type": {"$ref": "#/components/schemas/AssetType"}, "name": {"type": "string", "minLength": 1, "title": "Name"}}, "type": "object", "required": ["payload", "asset_type", "name"], "title": "EventInputAsset"}, "EventMetadataAPIOutput": {"properties": {"origin": {"$ref": "#/components/schemas/Origin"}, "source_service": {"$ref": "#/components/schemas/SourceService"}}, "type": "object", "required": ["origin", "source_service"], "title": "EventMetadataAPIOutput"}, "EventMetadataInput": {"properties": {"origin": {"$ref": "#/components/schemas/InsertableOrigin"}, "origin_device": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Origin Device"}, "source_os": {"$ref": "#/components/schemas/SourceOS"}, "source_service": {"$ref": "#/components/schemas/SourceService"}}, "type": "object", "required": ["origin", "origin_device", "source_os", "source_service"], "title": "EventMetadataInput"}, "EventPlanExtension": {"properties": {"plan_id": {"type": "string", "format": "uuid", "title": "Plan Id"}}, "type": "object", "required": ["plan_id"], "title": "EventPlanExtension"}, "EventTemplateAPIOutput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "minLength": 1, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "archived_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Archived At"}, "document_name": {"type": "string", "minLength": 1, "title": "Document Name"}, "document": {"oneOf": [{"$ref": "#/components/schemas/AudioTemplatePayload"}, {"$ref": "#/components/schemas/ContentTemplatePayload"}, {"$ref": "#/components/schemas/ImageTemplatePayload"}, {"$ref": "#/components/schemas/InteractiveTemplatePayload"}, {"$ref": "#/components/schemas/TextTemplatePayload"}, {"$ref": "#/components/schemas/VideoTemplatePayload"}, {"$ref": "#/components/schemas/BloodGlucoseTemplatePayload"}, {"$ref": "#/components/schemas/BloodPressureTemplatePayload"}, {"$ref": "#/components/schemas/BodyMetricTemplatePayload"}, {"$ref": "#/components/schemas/ExerciseTemplatePayload"}, {"$ref": "#/components/schemas/CardioTemplatePayload"}, {"$ref": "#/components/schemas/StrengthTemplatePayload"}, {"$ref": "#/components/schemas/EmotionTemplatePayload"}, {"$ref": "#/components/schemas/StressTemplatePayload"}, {"$ref": "#/components/schemas/DrinkTemplatePayload"}, {"$ref": "#/components/schemas/FoodTemplatePayload"}, {"$ref": "#/components/schemas/SupplementTemplatePayload"}, {"$ref": "#/components/schemas/CoreEventTemplatePayload"}, {"$ref": "#/components/schemas/NoteTemplatePayload"}, {"$ref": "#/components/schemas/SymptomTemplatePayload"}, {"$ref": "#/components/schemas/MedicationTemplatePayload-Output"}, {"$ref": "#/components/schemas/SleepV3TemplatePayload"}], "title": "Document", "discriminator": {"propertyName": "type", "mapping": {"audio": "#/components/schemas/AudioTemplatePayload", "blood_glucose": "#/components/schemas/BloodGlucoseTemplatePayload", "blood_pressure": "#/components/schemas/BloodPressureTemplatePayload", "body_metric": "#/components/schemas/BodyMetricTemplatePayload", "cardio": "#/components/schemas/CardioTemplatePayload", "content": "#/components/schemas/ContentTemplatePayload", "core_event": "#/components/schemas/CoreEventTemplatePayload", "drink": "#/components/schemas/DrinkTemplatePayload", "emotion": "#/components/schemas/EmotionTemplatePayload", "exercise": "#/components/schemas/ExerciseTemplatePayload", "food": "#/components/schemas/FoodTemplatePayload", "image": "#/components/schemas/ImageTemplatePayload", "interactive": "#/components/schemas/InteractiveTemplatePayload", "medication": "#/components/schemas/MedicationTemplatePayload-Output", "note": "#/components/schemas/NoteTemplatePayload", "sleep": "#/components/schemas/SleepV3TemplatePayload", "strength": "#/components/schemas/StrengthTemplatePayload", "stress": "#/components/schemas/StressTemplatePayload", "supplement": "#/components/schemas/SupplementTemplatePayload", "symptom": "#/components/schemas/SymptomTemplatePayload", "text": "#/components/schemas/TextTemplatePayload", "video": "#/components/schemas/VideoTemplatePayload"}}}, "document_type": {"$ref": "#/components/schemas/EventType"}}, "type": "object", "required": ["id", "system_properties", "name", "tags", "archived_at", "document_name", "document", "document_type"], "title": "EventTemplateAPIOutput"}, "EventType": {"type": "string", "enum": ["audio", "content", "image", "interactive", "text", "video", "blood_glucose", "blood_pressure", "body_metric", "cardio", "exercise", "strength", "emotion", "stress", "drink", "food", "supplement", "core_event", "sleep", "event_group", "note", "symptom", "medication", "HeartRate", "RestingHeartRate", "Sleep", "Steps", "DiaryEvents", "Location"], "title": "EventType"}, "EventTypedQueryAPI-Input": {"properties": {"types": {"items": {"$ref": "#/components/schemas/EventType"}, "type": "array", "minItems": 1, "title": "Types"}, "query": {"anyOf": [{"oneOf": [{"$ref": "#/components/schemas/ValuesQueryAPI"}, {"$ref": "#/components/schemas/PatternQueryAPI"}, {"$ref": "#/components/schemas/RangeQueryAPI"}, {"$ref": "#/components/schemas/ExistsQueryAPI"}, {"$ref": "#/components/schemas/RadiusQueryAPI"}], "discriminator": {"propertyName": "type", "mapping": {"exists": "#/components/schemas/ExistsQueryAPI", "pattern": "#/components/schemas/PatternQueryAPI", "radius": "#/components/schemas/RadiusQueryAPI", "range": "#/components/schemas/RangeQueryAPI", "values": "#/components/schemas/ValuesQueryAPI"}}}, {"$ref": "#/components/schemas/CompoundBooleanQueryAPI-Input"}, {"type": "null"}], "title": "Query"}}, "type": "object", "required": ["types"], "title": "EventTypedQueryAPI"}, "EventTypedQueryAPI-Output": {"properties": {"types": {"items": {"$ref": "#/components/schemas/EventType"}, "type": "array", "minItems": 1, "title": "Types"}, "query": {"anyOf": [{"oneOf": [{"$ref": "#/components/schemas/ValuesQueryAPI"}, {"$ref": "#/components/schemas/PatternQueryAPI"}, {"$ref": "#/components/schemas/RangeQueryAPI"}, {"$ref": "#/components/schemas/ExistsQueryAPI"}, {"$ref": "#/components/schemas/RadiusQueryAPI"}], "discriminator": {"propertyName": "type", "mapping": {"exists": "#/components/schemas/ExistsQueryAPI", "pattern": "#/components/schemas/PatternQueryAPI", "radius": "#/components/schemas/RadiusQueryAPI", "range": "#/components/schemas/RangeQueryAPI", "values": "#/components/schemas/ValuesQueryAPI"}}}, {"$ref": "#/components/schemas/CompoundBooleanQueryAPI-Output"}, {"type": "null"}], "title": "Query"}}, "type": "object", "required": ["types"], "title": "EventTypedQueryAPI"}, "EventV3Type": {"type": "string", "enum": ["audio", "content", "interactive", "image", "text", "video", "blood_glucose", "blood_pressure", "body_metric", "emotion", "stress", "exercise", "cardio", "strength", "drink", "food", "supplement", "core_event", "note", "symptom", "event_group", "medication", "sleep"], "title": "EventV3Type"}, "ExerciseAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "exercise", "title": "Type"}, "category": {"$ref": "#/components/schemas/ExerciseCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "rating", "duration"], "title": "ExerciseAPIOutput"}, "ExerciseCategory": {"type": "string", "enum": ["static_balance", "dynamic_balance", "stability", "daily_activities", "rehabilitation", "injury_prevention", "static_stretching", "dynamic_stretching", "joint_mobility", "muscle_activation", "self_massage", "sword_arts", "basketball", "volleyball", "tennis", "pickleball", "badminton", "squash", "soccer", "ultimate_frisbee", "rugby", "american_football", "flag_football", "archery", "bowling", "table_tennis", "golf", "cricket", "lacrosse", "field_hockey", "handball", "boxing", "kickboxing", "wrestling", "judo", "activity_other", "sport_other", "exercise"], "title": "ExerciseCategory"}, "ExerciseTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "exercise", "title": "Type"}, "category": {"$ref": "#/components/schemas/ExerciseCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "rating"], "title": "ExerciseTemplatePayload"}, "ExistsQueryAPI": {"properties": {"type": {"type": "string", "const": "exists", "title": "Type"}, "field_name": {"type": "string", "minLength": 1, "title": "Field Name"}}, "type": "object", "required": ["type", "field_name"], "title": "ExistsQueryAPI"}, "FetchAssetUrlAPIOutput": {"properties": {"assets": {"patternProperties": {"^[A-Za-z0-9._-]+$": {"type": "string"}}, "type": "object", "title": "Assets"}}, "type": "object", "required": ["assets"], "title": "FetchAssetUrlAPIOutput"}, "FoodAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "type": {"type": "string", "const": "food", "title": "Type"}, "category": {"$ref": "#/components/schemas/FoodCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "rating", "consumed_amount", "calories", "flavor", "type", "category", "consumed_type", "duration"], "title": "FoodAPIOutput"}, "FoodCategory": {"type": "string", "enum": ["meat", "fish", "eggs", "dairy", "fruits", "poultry", "seafood", "legumes", "ready_meal", "vegetables", "whole_grain", "fats_and_oils", "homemade_meal", "fast_food_meal", "refined_grains", "restaurant_meal", "sweets_and_desserts", "condiments_seasonings", "plant_based_alternatives", "snacks", "other"], "title": "FoodCategory"}, "FoodTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "items_per_serving": {"anyOf": [{"type": "number", "ge": 0, "le": 1000}, {"type": "null"}], "title": "Items Per Serving"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "amount_volume": {"anyOf": [{"type": "number", "ge": 0, "le": 1000}, {"type": "null"}], "title": "Amount Volume"}, "unit_volume": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit", "maxLength": 32}, {"type": "null"}]}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "type": {"type": "string", "const": "food", "title": "Type"}, "category": {"$ref": "#/components/schemas/FoodCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}, "amount_mass": {"anyOf": [{"type": "number", "ge": 0, "le": 1000}, {"type": "null"}], "title": "Amount Mass"}, "unit_mass": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit", "maxLength": 32}, {"type": "null"}]}}, "type": "object", "required": ["name", "duration", "note", "brand", "rating", "items_per_serving", "consumed_amount", "amount_volume", "unit_volume", "calories", "flavor", "nutrients", "type", "category", "consumed_type", "amount_mass", "unit_mass"], "title": "FoodTemplatePayload"}, "FrequencyDistributionAPIOutput": {"properties": {"data": {"additionalProperties": {"additionalProperties": {"type": "integer"}, "type": "object"}, "propertyNames": {"$ref": "#/components/schemas/DocumentType"}, "type": "object", "title": "Data"}}, "type": "object", "required": ["data"], "title": "FrequencyDistributionAPIOutput"}, "FrequencyDistributionAPIRequestInput": {"properties": {"queries": {"items": {"$ref": "#/components/schemas/DocumentTypedQueryAPI"}, "type": "array", "title": "Queries"}, "field_name": {"type": "string", "minLength": 1, "title": "Field Name"}}, "type": "object", "required": ["field_name"], "title": "FrequencyDistributionAPIRequestInput"}, "FrequencyDistributionAggregate": {"properties": {"aggregation_key": {"anyOf": [{"type": "number"}, {"type": "integer"}, {"type": "string"}], "title": "Aggregation Key"}, "document_count": {"type": "integer", "title": "Document Count"}}, "type": "object", "required": ["aggregation_key", "document_count"], "title": "FrequencyDistributionAggregate"}, "GroupTemplateAPIOutput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "minLength": 1, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "archived_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Archived At"}, "template_ids": {"items": {"type": "string", "format": "uuid"}, "type": "array", "title": "Template Ids"}}, "type": "object", "required": ["id", "system_properties", "name", "tags", "archived_at", "template_ids"], "title": "GroupTemplateAPIOutput"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "HeartRateAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "duration": {"anyOf": [{"type": "integer", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "bpm_avg": {"type": "number", "maximum": 400.0, "minimum": 0.0, "title": "Bpm Avg"}, "bpm_max": {"type": "number", "maximum": 400.0, "minimum": 0.0, "title": "Bpm Max"}, "bpm_min": {"type": "number", "maximum": 400.0, "minimum": 0.0, "title": "Bpm Min"}, "bpm_detail": {"anyOf": [{"items": {"$ref": "#/components/schemas/HeartRateBpmDetail"}, "type": "array"}, {"type": "null"}], "title": "Bpm Detail"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchema"}, "_doc_id": {"type": "string", "format": "uuid", "title": "Doc Id"}, "metadata": {"$ref": "#/components/schemas/MetadataOutput"}, "type": {"type": "string", "const": "HeartRate", "title": "Type"}}, "type": "object", "required": ["timestamp", "bpm_avg", "bpm_max", "bpm_min", "_doc_id", "metadata", "type"], "title": "HeartRateAPIOutput"}, "HeartRateBpmDetail": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "value": {"type": "number", "maximum": 400.0, "minimum": 0.0, "title": "Value"}, "confidence": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Confidence"}}, "type": "object", "required": ["timestamp", "value"], "title": "HeartRateBpmDetail"}, "ImageAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "image", "title": "Type"}, "category": {"$ref": "#/components/schemas/ImageCategory"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "title", "type", "category", "duration"], "title": "ImageAPIOutput"}, "ImageCategory": {"type": "string", "enum": ["image"], "title": "ImageCategory"}, "ImageTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "image", "title": "Type"}, "category": {"$ref": "#/components/schemas/ImageCategory"}}, "type": "object", "required": ["name", "duration", "note", "title", "url", "rating", "type", "category"], "title": "ImageTemplatePayload"}, "InsertAudioInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "audio", "title": "Type"}, "category": {"$ref": "#/components/schemas/AudioCategory"}}, "type": "object", "required": ["timestamp", "name", "type", "category"], "title": "InsertAudioInput"}, "InsertAudioInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "audio", "title": "Type"}, "category": {"$ref": "#/components/schemas/AudioCategory"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "content_hash"], "title": "InsertAudioInput"}, "InsertBloodGlucoseInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "blood_glucose", "title": "Type"}, "category": {"$ref": "#/components/schemas/BloodGlucoseCategory"}, "value": {"type": "number", "maximum": 4000.0, "minimum": 0.0, "title": "Value"}, "specimen_source": {"$ref": "#/components/schemas/BloodGlucoseSpecimenSource", "default": "unknown"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category", "value", "note"], "title": "InsertBloodGlucoseInput"}, "InsertBloodGlucoseInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "blood_glucose", "title": "Type"}, "category": {"$ref": "#/components/schemas/BloodGlucoseCategory"}, "value": {"type": "number", "maximum": 4000.0, "minimum": 0.0, "title": "Value"}, "specimen_source": {"$ref": "#/components/schemas/BloodGlucoseSpecimenSource", "default": "unknown"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "value", "note", "content_hash"], "title": "InsertBloodGlucoseInput"}, "InsertBloodPressureInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "blood_pressure", "title": "Type"}, "category": {"$ref": "#/components/schemas/BloodPressureCategory", "default": "blood_pressure"}, "systolic": {"type": "number", "maximum": 500.0, "minimum": 0.0, "title": "Systolic"}, "diastolic": {"type": "number", "maximum": 500.0, "minimum": 0.0, "title": "Diastolic"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "systolic", "diastolic", "note"], "title": "InsertBloodPressureInput"}, "InsertBloodPressureInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "blood_pressure", "title": "Type"}, "category": {"$ref": "#/components/schemas/BloodPressureCategory", "default": "blood_pressure"}, "systolic": {"type": "number", "maximum": 500.0, "minimum": 0.0, "title": "Systolic"}, "diastolic": {"type": "number", "maximum": 500.0, "minimum": 0.0, "title": "Diastolic"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "systolic", "diastolic", "note", "content_hash"], "title": "InsertBloodPressureInput"}, "InsertBodyMetricInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "body_metric", "title": "Type"}, "category": {"$ref": "#/components/schemas/BodyMetricCategory"}, "value": {"type": "number", "maximum": 1000000000.0, "minimum": -1000000000.0, "title": "Value"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category", "value", "note"], "title": "InsertBodyMetricInput"}, "InsertBodyMetricInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "body_metric", "title": "Type"}, "category": {"$ref": "#/components/schemas/BodyMetricCategory"}, "value": {"type": "number", "maximum": 1000000000.0, "minimum": -1000000000.0, "title": "Value"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "value", "note", "content_hash"], "title": "InsertBodyMetricInput"}, "InsertCardioInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "cardio", "title": "Type"}, "category": {"$ref": "#/components/schemas/CardioCategory"}, "distance": {"anyOf": [{"type": "number", "ge": 0, "le": 100000}, {"type": "null"}], "title": "Distance"}, "elevation": {"anyOf": [{"type": "number", "ge": -500, "le": 8848}, {"type": "null"}], "title": "Elevation"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category"], "title": "InsertCardioInput"}, "InsertCardioInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "cardio", "title": "Type"}, "category": {"$ref": "#/components/schemas/CardioCategory"}, "distance": {"anyOf": [{"type": "number", "ge": 0, "le": 100000}, {"type": "null"}], "title": "Distance"}, "elevation": {"anyOf": [{"type": "number", "ge": -500, "le": 8848}, {"type": "null"}], "title": "Elevation"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "content_hash"], "title": "InsertCardioInput"}, "InsertContentInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "content", "title": "Type"}, "category": {"$ref": "#/components/schemas/ContentCategory"}}, "type": "object", "required": ["timestamp", "name", "type", "category"], "title": "InsertContentInput"}, "InsertContentInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "content", "title": "Type"}, "category": {"$ref": "#/components/schemas/ContentCategory"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "content_hash"], "title": "InsertContentInput"}, "InsertCoreEventInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "core_event", "title": "Type"}, "category": {"$ref": "#/components/schemas/CoreEventCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category"], "title": "InsertCoreEventInput"}, "InsertCoreEventInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "core_event", "title": "Type"}, "category": {"$ref": "#/components/schemas/CoreEventCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "content_hash"], "title": "InsertCoreEventInput"}, "InsertDrinkInput-Input": {"properties": {"brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "drink", "title": "Type"}, "category": {"$ref": "#/components/schemas/DrinkCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}}, "type": "object", "required": ["brand", "rating", "note", "consumed_amount", "calories", "flavor", "nutrients", "timestamp", "name", "type", "category", "consumed_type"], "title": "InsertDrinkInput"}, "InsertDrinkInput-Output": {"properties": {"brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "drink", "title": "Type"}, "category": {"$ref": "#/components/schemas/DrinkCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["brand", "rating", "note", "consumed_amount", "calories", "flavor", "nutrients", "timestamp", "name", "type", "category", "consumed_type", "content_hash"], "title": "InsertDrinkInput"}, "InsertEmotionInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "emotion", "title": "Type"}, "category": {"$ref": "#/components/schemas/EmotionCategory"}, "rating": {"type": "integer", "maximum": 10.0, "minimum": 0.0, "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category", "rating"], "title": "InsertEmotionInput"}, "InsertEmotionInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "emotion", "title": "Type"}, "category": {"$ref": "#/components/schemas/EmotionCategory"}, "rating": {"type": "integer", "maximum": 10.0, "minimum": 0.0, "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "rating", "content_hash"], "title": "InsertEmotionInput"}, "InsertEventAPIRequestInput": {"properties": {"documents": {"items": {"oneOf": [{"$ref": "#/components/schemas/InsertBloodGlucoseInput-Input"}, {"$ref": "#/components/schemas/InsertBloodPressureInput-Input"}, {"$ref": "#/components/schemas/InsertBodyMetricInput-Input"}, {"$ref": "#/components/schemas/InsertAudioInput-Input"}, {"$ref": "#/components/schemas/InsertContentInput-Input"}, {"$ref": "#/components/schemas/InsertImageInput-Input"}, {"$ref": "#/components/schemas/InsertInteractiveInput-Input"}, {"$ref": "#/components/schemas/InsertTextInput-Input"}, {"$ref": "#/components/schemas/InsertVideoInput-Input"}, {"$ref": "#/components/schemas/InsertCardioInput-Input"}, {"$ref": "#/components/schemas/InsertExerciseInput-Input"}, {"$ref": "#/components/schemas/InsertStrengthInput-Input"}, {"$ref": "#/components/schemas/InsertEmotionInput-Input"}, {"$ref": "#/components/schemas/InsertStressInput-Input"}, {"$ref": "#/components/schemas/InsertDrinkInput-Input"}, {"$ref": "#/components/schemas/InsertFoodInput-Input"}, {"$ref": "#/components/schemas/InsertSupplementInput-Input"}, {"$ref": "#/components/schemas/InsertCoreEventInput-Input"}, {"$ref": "#/components/schemas/InsertSleepV3Input-Input"}, {"$ref": "#/components/schemas/InsertEventGroupInput-Input"}, {"$ref": "#/components/schemas/InsertNoteInput-Input"}, {"$ref": "#/components/schemas/InsertSymptomInput-Input"}, {"$ref": "#/components/schemas/InsertMedicationInput-Input"}], "discriminator": {"propertyName": "type", "mapping": {"audio": "#/components/schemas/InsertAudioInput-Input", "blood_glucose": "#/components/schemas/InsertBloodGlucoseInput-Input", "blood_pressure": "#/components/schemas/InsertBloodPressureInput-Input", "body_metric": "#/components/schemas/InsertBodyMetricInput-Input", "cardio": "#/components/schemas/InsertCardioInput-Input", "content": "#/components/schemas/InsertContentInput-Input", "core_event": "#/components/schemas/InsertCoreEventInput-Input", "drink": "#/components/schemas/InsertDrinkInput-Input", "emotion": "#/components/schemas/InsertEmotionInput-Input", "event_group": "#/components/schemas/InsertEventGroupInput-Input", "exercise": "#/components/schemas/InsertExerciseInput-Input", "food": "#/components/schemas/InsertFoodInput-Input", "image": "#/components/schemas/InsertImageInput-Input", "interactive": "#/components/schemas/InsertInteractiveInput-Input", "medication": "#/components/schemas/InsertMedicationInput-Input", "note": "#/components/schemas/InsertNoteInput-Input", "sleep": "#/components/schemas/InsertSleepV3Input-Input", "strength": "#/components/schemas/InsertStrengthInput-Input", "stress": "#/components/schemas/InsertStressInput-Input", "supplement": "#/components/schemas/InsertSupplementInput-Input", "symptom": "#/components/schemas/InsertSymptomInput-Input", "text": "#/components/schemas/InsertTextInput-Input", "video": "#/components/schemas/InsertVideoInput-Input"}}}, "type": "array", "minItems": 1, "title": "Documents"}, "metadata": {"$ref": "#/components/schemas/EventMetadataInput"}}, "type": "object", "required": ["documents", "metadata"], "title": "InsertEventAPIRequestInput"}, "InsertEventGroupInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "event_group", "title": "Type"}, "events": {"items": {"anyOf": [{"$ref": "#/components/schemas/InsertBloodGlucoseInput-Input"}, {"$ref": "#/components/schemas/InsertBloodPressureInput-Input"}, {"$ref": "#/components/schemas/InsertBodyMetricInput-Input"}, {"$ref": "#/components/schemas/InsertAudioInput-Input"}, {"$ref": "#/components/schemas/InsertContentInput-Input"}, {"$ref": "#/components/schemas/InsertImageInput-Input"}, {"$ref": "#/components/schemas/InsertInteractiveInput-Input"}, {"$ref": "#/components/schemas/InsertTextInput-Input"}, {"$ref": "#/components/schemas/InsertVideoInput-Input"}, {"$ref": "#/components/schemas/InsertCardioInput-Input"}, {"$ref": "#/components/schemas/InsertExerciseInput-Input"}, {"$ref": "#/components/schemas/InsertStrengthInput-Input"}, {"$ref": "#/components/schemas/InsertEmotionInput-Input"}, {"$ref": "#/components/schemas/InsertStressInput-Input"}, {"$ref": "#/components/schemas/InsertDrinkInput-Input"}, {"$ref": "#/components/schemas/InsertFoodInput-Input"}, {"$ref": "#/components/schemas/InsertSupplementInput-Input"}, {"$ref": "#/components/schemas/InsertCoreEventInput-Input"}, {"$ref": "#/components/schemas/InsertSleepV3Input-Input"}, {"$ref": "#/components/schemas/InsertEventGroupInput-Input"}, {"$ref": "#/components/schemas/InsertNoteInput-Input"}, {"$ref": "#/components/schemas/InsertSymptomInput-Input"}, {"$ref": "#/components/schemas/InsertMedicationInput-Input"}]}, "type": "array", "minItems": 1, "title": "Events"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "events"], "title": "InsertEventGroupInput"}, "InsertEventGroupInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "event_group", "title": "Type"}, "events": {"items": {"anyOf": [{"$ref": "#/components/schemas/InsertBloodGlucoseInput-Output"}, {"$ref": "#/components/schemas/InsertBloodPressureInput-Output"}, {"$ref": "#/components/schemas/InsertBodyMetricInput-Output"}, {"$ref": "#/components/schemas/InsertAudioInput-Output"}, {"$ref": "#/components/schemas/InsertContentInput-Output"}, {"$ref": "#/components/schemas/InsertImageInput-Output"}, {"$ref": "#/components/schemas/InsertInteractiveInput-Output"}, {"$ref": "#/components/schemas/InsertTextInput-Output"}, {"$ref": "#/components/schemas/InsertVideoInput-Output"}, {"$ref": "#/components/schemas/InsertCardioInput-Output"}, {"$ref": "#/components/schemas/InsertExerciseInput-Output"}, {"$ref": "#/components/schemas/InsertStrengthInput-Output"}, {"$ref": "#/components/schemas/InsertEmotionInput-Output"}, {"$ref": "#/components/schemas/InsertStressInput-Output"}, {"$ref": "#/components/schemas/InsertDrinkInput-Output"}, {"$ref": "#/components/schemas/InsertFoodInput-Output"}, {"$ref": "#/components/schemas/InsertSupplementInput-Output"}, {"$ref": "#/components/schemas/InsertCoreEventInput-Output"}, {"$ref": "#/components/schemas/InsertSleepV3Input-Output"}, {"$ref": "#/components/schemas/InsertEventGroupInput-Output"}, {"$ref": "#/components/schemas/InsertNoteInput-Output"}, {"$ref": "#/components/schemas/InsertSymptomInput-Output"}, {"$ref": "#/components/schemas/InsertMedicationInput-Output"}]}, "type": "array", "minItems": 1, "title": "Events"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "events", "content_hash"], "title": "InsertEventGroupInput"}, "InsertEventTemplateInputBoundaryItem": {"properties": {"name": {"type": "string", "minLength": 1, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "document": {"oneOf": [{"$ref": "#/components/schemas/AudioTemplatePayload"}, {"$ref": "#/components/schemas/ContentTemplatePayload"}, {"$ref": "#/components/schemas/ImageTemplatePayload"}, {"$ref": "#/components/schemas/InteractiveTemplatePayload"}, {"$ref": "#/components/schemas/TextTemplatePayload"}, {"$ref": "#/components/schemas/VideoTemplatePayload"}, {"$ref": "#/components/schemas/BloodGlucoseTemplatePayload"}, {"$ref": "#/components/schemas/BloodPressureTemplatePayload"}, {"$ref": "#/components/schemas/BodyMetricTemplatePayload"}, {"$ref": "#/components/schemas/ExerciseTemplatePayload"}, {"$ref": "#/components/schemas/CardioTemplatePayload"}, {"$ref": "#/components/schemas/StrengthTemplatePayload"}, {"$ref": "#/components/schemas/EmotionTemplatePayload"}, {"$ref": "#/components/schemas/StressTemplatePayload"}, {"$ref": "#/components/schemas/DrinkTemplatePayload"}, {"$ref": "#/components/schemas/FoodTemplatePayload"}, {"$ref": "#/components/schemas/SupplementTemplatePayload"}, {"$ref": "#/components/schemas/CoreEventTemplatePayload"}, {"$ref": "#/components/schemas/NoteTemplatePayload"}, {"$ref": "#/components/schemas/SymptomTemplatePayload"}, {"$ref": "#/components/schemas/MedicationTemplatePayload-Input"}, {"$ref": "#/components/schemas/SleepV3TemplatePayload"}], "title": "Document", "discriminator": {"propertyName": "type", "mapping": {"audio": "#/components/schemas/AudioTemplatePayload", "blood_glucose": "#/components/schemas/BloodGlucoseTemplatePayload", "blood_pressure": "#/components/schemas/BloodPressureTemplatePayload", "body_metric": "#/components/schemas/BodyMetricTemplatePayload", "cardio": "#/components/schemas/CardioTemplatePayload", "content": "#/components/schemas/ContentTemplatePayload", "core_event": "#/components/schemas/CoreEventTemplatePayload", "drink": "#/components/schemas/DrinkTemplatePayload", "emotion": "#/components/schemas/EmotionTemplatePayload", "exercise": "#/components/schemas/ExerciseTemplatePayload", "food": "#/components/schemas/FoodTemplatePayload", "image": "#/components/schemas/ImageTemplatePayload", "interactive": "#/components/schemas/InteractiveTemplatePayload", "medication": "#/components/schemas/MedicationTemplatePayload-Input", "note": "#/components/schemas/NoteTemplatePayload", "sleep": "#/components/schemas/SleepV3TemplatePayload", "strength": "#/components/schemas/StrengthTemplatePayload", "stress": "#/components/schemas/StressTemplatePayload", "supplement": "#/components/schemas/SupplementTemplatePayload", "symptom": "#/components/schemas/SymptomTemplatePayload", "text": "#/components/schemas/TextTemplatePayload", "video": "#/components/schemas/VideoTemplatePayload"}}}}, "type": "object", "required": ["name", "document"], "title": "InsertEventTemplateInputBoundaryItem"}, "InsertExerciseInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "exercise", "title": "Type"}, "category": {"$ref": "#/components/schemas/ExerciseCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category"], "title": "InsertExerciseInput"}, "InsertExerciseInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "exercise", "title": "Type"}, "category": {"$ref": "#/components/schemas/ExerciseCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "content_hash"], "title": "InsertExerciseInput"}, "InsertFoodInput-Input": {"properties": {"brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "food", "title": "Type"}, "category": {"$ref": "#/components/schemas/FoodCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}}, "type": "object", "required": ["brand", "rating", "note", "consumed_amount", "calories", "flavor", "nutrients", "timestamp", "name", "type", "category", "consumed_type"], "title": "InsertFoodInput"}, "InsertFoodInput-Output": {"properties": {"brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "food", "title": "Type"}, "category": {"$ref": "#/components/schemas/FoodCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["brand", "rating", "note", "consumed_amount", "calories", "flavor", "nutrients", "timestamp", "name", "type", "category", "consumed_type", "content_hash"], "title": "InsertFoodInput"}, "InsertGroupTemplateInputBoundaryItem": {"properties": {"name": {"type": "string", "minLength": 1, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "templates": {"items": {"$ref": "#/components/schemas/InsertEventTemplateInputBoundaryItem"}, "type": "array", "minItems": 1, "title": "Templates"}}, "type": "object", "required": ["name", "templates"], "title": "InsertGroupTemplateInputBoundaryItem"}, "InsertImageInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "image", "title": "Type"}, "category": {"$ref": "#/components/schemas/ImageCategory"}}, "type": "object", "required": ["timestamp", "name", "type", "category"], "title": "InsertImageInput"}, "InsertImageInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "image", "title": "Type"}, "category": {"$ref": "#/components/schemas/ImageCategory"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "content_hash"], "title": "InsertImageInput"}, "InsertInteractiveInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "interactive", "title": "Type"}, "category": {"$ref": "#/components/schemas/InteractiveCategory"}}, "type": "object", "required": ["timestamp", "name", "type", "category"], "title": "InsertInteractiveInput"}, "InsertInteractiveInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "interactive", "title": "Type"}, "category": {"$ref": "#/components/schemas/InteractiveCategory"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "content_hash"], "title": "InsertInteractiveInput"}, "InsertMedicationInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "medication", "title": "Type"}, "category": {"$ref": "#/components/schemas/MedicationCategory"}, "medication_details": {"$ref": "#/components/schemas/MedicationDetails"}, "single_dose_information": {"$ref": "#/components/schemas/SingleDoseInformation"}, "consumed_amount": {"type": "number", "maximum": 1000.0, "minimum": 0.0, "title": "Consumed Amount"}, "consume_unit": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/ConsumeUnit"}], "title": "Consume Unit"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category", "medication_details", "single_dose_information", "consumed_amount", "consume_unit"], "title": "InsertMedicationInput"}, "InsertMedicationInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "medication", "title": "Type"}, "category": {"$ref": "#/components/schemas/MedicationCategory"}, "medication_details": {"$ref": "#/components/schemas/MedicationDetails"}, "single_dose_information": {"$ref": "#/components/schemas/SingleDoseInformation"}, "consumed_amount": {"type": "number", "maximum": 1000.0, "minimum": 0.0, "title": "Consumed Amount"}, "consume_unit": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/ConsumeUnit"}], "title": "Consume Unit"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "medication_details", "single_dose_information", "consumed_amount", "consume_unit", "content_hash"], "title": "InsertMedicationInput"}, "InsertNoteInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "note", "title": "Type"}, "category": {"$ref": "#/components/schemas/NoteCategory"}, "note": {"type": "string", "maxLength": 8196, "minLength": 1, "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category", "note"], "title": "InsertNoteInput"}, "InsertNoteInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "note", "title": "Type"}, "category": {"$ref": "#/components/schemas/NoteCategory"}, "note": {"type": "string", "maxLength": 8196, "minLength": 1, "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "note", "content_hash"], "title": "InsertNoteInput"}, "InsertPlanInput": {"properties": {"type": {"type": "string", "const": "plan", "title": "Type"}, "name": {"type": "string", "maxLength": 64, "minLength": 1, "title": "Name"}, "template_id": {"type": "string", "format": "uuid", "title": "Template Id"}, "is_urgent": {"type": "boolean", "title": "<PERSON>"}, "is_confirmation_required": {"type": "boolean", "title": "Is Confirmation Required"}, "streak": {"$ref": "#/components/schemas/PlanStreak", "default": {"streak": 0, "longest_streak": 0, "total_triggered": 0}}, "recurrence": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Recurrence", "description": "Expects format of 'DTSTART:'tz_aware_iso8601}\n{OPTS}"}, "is_absolute_schedule": {"type": "boolean", "title": "Is Absolute Schedule"}, "priority": {"$ref": "#/components/schemas/Priority", "default": 0}, "prompt": {"type": "string", "minLength": 1, "title": "Prompt"}, "current_completed": {"type": "integer", "minimum": 0.0, "title": "Current Completed"}, "next_scheduled_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Next Scheduled At"}, "note": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Note"}, "max_completed": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Max Completed"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}}, "type": "object", "required": ["type", "name", "template_id", "is_urgent", "is_confirmation_required", "recurrence", "is_absolute_schedule", "prompt", "current_completed"], "title": "InsertPlanInput"}, "InsertPlansAPIRequestInput": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/InsertPlanInput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "InsertPlansAPIRequestInput"}, "InsertRecordAPIRequestInput": {"properties": {"documents": {"items": {"oneOf": [{"$ref": "#/components/schemas/InsertSleepRecordInput"}], "discriminator": {"propertyName": "type", "mapping": {"sleep_record": "#/components/schemas/InsertSleepRecordInput"}}}, "type": "array", "minItems": 1, "title": "Documents"}, "metadata": {"$ref": "#/components/schemas/EventMetadataInput"}}, "type": "object", "required": ["documents", "metadata"], "title": "InsertRecordAPIRequestInput"}, "InsertSeparateGroupTemplateInputBoundaryItem": {"properties": {"name": {"type": "string", "minLength": 1, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "template_ids": {"items": {"type": "string", "format": "uuid"}, "type": "array", "minItems": 1, "title": "Template Ids"}}, "type": "object", "required": ["name", "template_ids"], "title": "InsertSeparateGroupTemplateInputBoundaryItem"}, "InsertSleepRecordInput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"type": "string", "format": "date-time", "title": "End Time"}, "type": {"type": "string", "const": "sleep_record", "title": "Type"}, "stage": {"$ref": "#/components/schemas/SleepStage"}}, "type": "object", "required": ["timestamp", "end_time", "type", "stage"], "title": "InsertSleepRecordInput"}, "InsertSleepV3Input-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "sleep", "title": "Type"}, "category": {"$ref": "#/components/schemas/SleepV3Category"}, "deep_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Deep Seconds"}, "light_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Light Seconds"}, "rem_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "awake_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Awake Seconds"}, "restless_moments": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Restless Moments"}, "provider_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Provider Score"}, "llif_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category", "deep_seconds", "light_seconds", "rem_seconds", "awake_seconds", "restless_moments", "provider_score", "llif_score"], "title": "InsertSleepV3Input"}, "InsertSleepV3Input-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "sleep", "title": "Type"}, "category": {"$ref": "#/components/schemas/SleepV3Category"}, "deep_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Deep Seconds"}, "light_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Light Seconds"}, "rem_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "awake_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Awake Seconds"}, "restless_moments": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Restless Moments"}, "provider_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Provider Score"}, "llif_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "deep_seconds", "light_seconds", "rem_seconds", "awake_seconds", "restless_moments", "provider_score", "llif_score", "content_hash"], "title": "InsertSleepV3Input"}, "InsertStrengthInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "strength", "title": "Type"}, "category": {"$ref": "#/components/schemas/StrengthCategory"}, "count": {"type": "integer", "maximum": 1000.0, "minimum": 0.0, "title": "Count"}, "weight": {"anyOf": [{"type": "number", "ge": 0, "le": 500}, {"type": "null"}], "title": "Weight"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category", "count"], "title": "InsertStrengthInput"}, "InsertStrengthInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "strength", "title": "Type"}, "category": {"$ref": "#/components/schemas/StrengthCategory"}, "count": {"type": "integer", "maximum": 1000.0, "minimum": 0.0, "title": "Count"}, "weight": {"anyOf": [{"type": "number", "ge": 0, "le": 500}, {"type": "null"}], "title": "Weight"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "count", "content_hash"], "title": "InsertStrengthInput"}, "InsertStressInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "stress", "title": "Type"}, "category": {"$ref": "#/components/schemas/StressCategory"}, "rating": {"type": "integer", "maximum": 5.0, "minimum": -5.0, "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["timestamp", "name", "type", "category", "rating"], "title": "InsertStressInput"}, "InsertStressInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "stress", "title": "Type"}, "category": {"$ref": "#/components/schemas/StressCategory"}, "rating": {"type": "integer", "maximum": 5.0, "minimum": -5.0, "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "rating", "content_hash"], "title": "InsertStressInput"}, "InsertSupplementInput-Input": {"properties": {"brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "supplement", "title": "Type"}, "category": {"$ref": "#/components/schemas/SupplementCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}}, "type": "object", "required": ["brand", "rating", "note", "consumed_amount", "calories", "flavor", "nutrients", "timestamp", "name", "type", "category", "consumed_type"], "title": "InsertSupplementInput"}, "InsertSupplementInput-Output": {"properties": {"brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "supplement", "title": "Type"}, "category": {"$ref": "#/components/schemas/SupplementCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["brand", "rating", "note", "consumed_amount", "calories", "flavor", "nutrients", "timestamp", "name", "type", "category", "consumed_type", "content_hash"], "title": "InsertSupplementInput"}, "InsertSymptomInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "symptom", "title": "Type"}, "category": {"$ref": "#/components/schemas/SymptomCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "body_parts": {"items": {"$ref": "#/components/schemas/BodyParts"}, "type": "array", "title": "Body Parts"}}, "type": "object", "required": ["timestamp", "name", "type", "category", "body_parts"], "title": "InsertSymptomInput"}, "InsertSymptomInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "symptom", "title": "Type"}, "category": {"$ref": "#/components/schemas/SymptomCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "body_parts": {"items": {"$ref": "#/components/schemas/BodyParts"}, "type": "array", "title": "Body Parts"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "body_parts", "content_hash"], "title": "InsertSymptomInput"}, "InsertTemplateAPIRequestInput": {"properties": {"documents": {"items": {"anyOf": [{"$ref": "#/components/schemas/InsertSeparateGroupTemplateInputBoundaryItem"}, {"$ref": "#/components/schemas/InsertEventTemplateInputBoundaryItem"}, {"$ref": "#/components/schemas/InsertGroupTemplateInputBoundaryItem"}]}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "InsertTemplateAPIRequestInput"}, "InsertTextInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "text", "title": "Type"}, "category": {"$ref": "#/components/schemas/TextCategory"}}, "type": "object", "required": ["timestamp", "name", "type", "category"], "title": "InsertTextInput"}, "InsertTextInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "text", "title": "Type"}, "category": {"$ref": "#/components/schemas/TextCategory"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "content_hash"], "title": "InsertTextInput"}, "InsertUseCaseAPIRequestInput": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/InsertUseCaseInput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "InsertUseCaseAPIRequestInput"}, "InsertUseCaseInput": {"properties": {"name": {"type": "string", "minLength": 1, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}}, "type": "object", "required": ["name"], "title": "InsertUseCaseInput"}, "InsertVideoInput-Input": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "video", "title": "Type"}, "category": {"$ref": "#/components/schemas/VideoCategory"}}, "type": "object", "required": ["timestamp", "name", "type", "category"], "title": "InsertVideoInput"}, "InsertVideoInput-Output": {"properties": {"assets": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "video", "title": "Type"}, "category": {"$ref": "#/components/schemas/VideoCategory"}, "content_hash": {"type": "string", "title": "Content Hash", "readOnly": true}}, "type": "object", "required": ["timestamp", "name", "type", "category", "content_hash"], "title": "InsertVideoInput"}, "InsertableOrigin": {"type": "string", "enum": ["amazon", "apple", "fitbit", "google", "netflix", "oura", "ll<PERSON>", "best_life"], "title": "InsertableOrigin"}, "InteractiveAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "interactive", "title": "Type"}, "category": {"$ref": "#/components/schemas/InteractiveCategory"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "title", "type", "category", "duration"], "title": "InteractiveAPIOutput"}, "InteractiveCategory": {"type": "string", "enum": ["interactive", "game", "virtual_reality", "augmented_reality", "board_game", "card_game", "puzzle", "quiz", "app"], "title": "InteractiveCategory"}, "InteractiveTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "interactive", "title": "Type"}, "category": {"$ref": "#/components/schemas/InteractiveCategory"}}, "type": "object", "required": ["name", "duration", "note", "title", "url", "rating", "type", "category"], "title": "InteractiveTemplatePayload"}, "LocationAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "duration": {"anyOf": [{"type": "integer", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "start_coordinates": {"$ref": "#/components/schemas/CoordinatesModel"}, "end_coordinates": {"anyOf": [{"$ref": "#/components/schemas/CoordinatesModel"}, {"type": "null"}]}, "average_coordinates": {"anyOf": [{"$ref": "#/components/schemas/CoordinatesModel"}, {"type": "null"}]}, "start_altitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Start Altitude"}, "place_visit_details": {"anyOf": [{"$ref": "#/components/schemas/PlaceVisitDetails"}, {"type": "null"}]}, "waypoint_details": {"anyOf": [{"items": {"$ref": "#/components/schemas/WaypointDetails"}, "type": "array"}, {"type": "null"}], "title": "Waypoint Details"}, "end_altitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "End Altitude"}, "average_altitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Altitude"}, "distance": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Distance"}, "activity_type": {"anyOf": [{"$ref": "#/components/schemas/ActivityType"}, {"type": "null"}]}, "activity_type_probability": {"anyOf": [{"type": "number", "maximum": 1.0, "minimum": 0.0}, {"type": "null"}], "title": "Activity Type Probability"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchema"}, "_doc_id": {"type": "string", "format": "uuid", "title": "Doc Id"}, "metadata": {"$ref": "#/components/schemas/MetadataOutput"}, "type": {"type": "string", "const": "Location", "title": "Type"}}, "type": "object", "required": ["timestamp", "start_coordinates", "_doc_id", "metadata", "type"], "title": "LocationAPIOutput"}, "LocationSource": {"type": "string", "enum": ["gps", "wifi", "cell", "unknown", "visit_departure", "visit_arrival"], "title": "LocationSource"}, "MatchType": {"type": "string", "enum": ["fuzzy", "exact", "default"], "title": "MatchType"}, "MedicationAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "medication", "title": "Type"}, "category": {"$ref": "#/components/schemas/MedicationCategory"}, "medication_details": {"$ref": "#/components/schemas/MedicationDetails"}, "single_dose_information": {"$ref": "#/components/schemas/SingleDoseInformation"}, "consumed_amount": {"type": "number", "maximum": 1000.0, "minimum": 0.0, "title": "Consumed Amount"}, "consume_unit": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/ConsumeUnit"}], "title": "Consume Unit"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "medication_details", "single_dose_information", "consumed_amount", "consume_unit", "duration"], "title": "MedicationAPIOutput"}, "MedicationCategory": {"type": "string", "enum": ["analgesics", "antibiotics", "antivirals", "antifungals", "antiparasitics", "cardiovascular", "respiratory", "gastrointestinal", "endocrine", "neurological", "mental_health", "immunosuppressants", "oncology", "vaccines", "dermatological", "ophthalmic", "otic", "musculoskeletal", "hematological", "reproductive", "urinary", "emergency"], "title": "MedicationCategory"}, "MedicationDetails": {"properties": {"brand": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Brand"}, "generic_name": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Generic Name"}, "rx_cuid": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Rx C<PERSON>", "description": "External identifier from RxNorm"}, "administration": {"$ref": "#/components/schemas/Administration"}}, "type": "object", "required": ["brand", "generic_name", "rx_cuid", "administration"], "title": "MedicationDetails"}, "MedicationTemplatePayload-Input": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "medication", "title": "Type"}, "category": {"$ref": "#/components/schemas/MedicationCategory"}, "medication_details": {"$ref": "#/components/schemas/MedicationDetails"}, "single_dose_information": {"$ref": "#/components/schemas/SingleDoseInformation"}, "consumed_amount": {"type": "number", "maximum": 1000.0, "minimum": 0.0, "title": "Consumed Amount"}, "consume_unit": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/ConsumeUnit"}], "title": "Consume Unit"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "medication_details", "single_dose_information", "consumed_amount", "consume_unit"], "title": "MedicationTemplatePayload"}, "MedicationTemplatePayload-Output": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "medication", "title": "Type"}, "category": {"$ref": "#/components/schemas/MedicationCategory"}, "medication_details": {"$ref": "#/components/schemas/MedicationDetails"}, "single_dose_information": {"$ref": "#/components/schemas/SingleDoseInformation"}, "consumed_amount": {"type": "number", "maximum": 1000.0, "minimum": 0.0, "title": "Consumed Amount"}, "consume_unit": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/ConsumeUnit"}], "title": "Consume Unit"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "medication_details", "single_dose_information", "consumed_amount", "consume_unit"], "title": "MedicationTemplatePayload"}, "MetadataOutput": {"properties": {"organization": {"$ref": "#/components/schemas/Organization"}, "data_integrity": {"$ref": "#/components/schemas/DataIntegrity"}, "important": {"type": "boolean", "title": "Important", "default": false}, "urgent": {"type": "boolean", "title": "<PERSON><PERSON>", "default": false}, "service": {"anyOf": [{"type": "string"}, {"$ref": "#/components/schemas/Service"}, {"type": "null"}], "title": "Service"}, "data_quality": {"anyOf": [{"$ref": "#/components/schemas/DataQuality"}, {"type": "null"}]}, "sync_software": {"anyOf": [{"$ref": "#/components/schemas/Application"}, {"type": "null"}]}, "sync_device": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sync Device"}, "sensor": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sensor"}, "data_proxy": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Data Proxy"}, "favorited_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Favorited At"}}, "type": "object", "required": ["organization", "data_integrity"], "title": "MetadataOutput"}, "ModifyEventAssetsAPIRequestInput": {"properties": {"items": {"items": {"$ref": "#/components/schemas/ModifyEventAssetsInputBoundaryItem"}, "type": "array", "minItems": 1, "title": "Items"}}, "type": "object", "required": ["items"], "title": "ModifyEventAssetsAPIRequestInput"}, "ModifyEventAssetsInputBoundaryItem": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "type": {"$ref": "#/components/schemas/EventV3Type"}, "assets_to_add": {"anyOf": [{"items": {"$ref": "#/components/schemas/EventInputAsset"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Assets To Add"}, "asset_ids_to_remove": {"anyOf": [{"items": {"type": "string"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset Ids To Remove"}}, "type": "object", "required": ["id", "type", "assets_to_add", "asset_ids_to_remove"], "title": "ModifyEventAssetsInputBoundaryItem"}, "NoteAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "note", "title": "Type"}, "category": {"$ref": "#/components/schemas/NoteCategory"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "duration"], "title": "NoteAPIOutput"}, "NoteCategory": {"type": "string", "enum": ["note", "journal", "gratitude"], "title": "NoteCategory"}, "NoteTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "note", "title": "Type"}, "category": {"$ref": "#/components/schemas/NoteCategory"}}, "type": "object", "required": ["name", "duration", "note", "type", "category"], "title": "NoteTemplatePayload"}, "Nutrients": {"properties": {"fat": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Fat"}, "saturated_fat": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Saturated Fat"}, "polyunsaturated_fat": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Polyunsaturated Fat"}, "monounsaturated_fat": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Monounsaturated Fat"}, "trans_fat": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Trans Fat"}, "cholesterol": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Cholesterol"}, "carbohydrates": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Carbohydrates"}, "fiber": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Fiber"}, "sugar": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Sugar"}, "protein": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "sodium": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Sodium"}, "potassium": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Potassium"}, "vitamin_a": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Vitamin A"}, "vitamin_c": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Vitamin C"}, "iron": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Iron"}, "calcium": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calcium"}, "biotin": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "caffeine": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Caffeine"}, "chloride": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Chloride"}, "chromium": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Chromium"}, "copper": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Copper"}, "folate": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Folate"}, "iodine": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Iodine"}, "magnesium": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Magnesium"}, "manganese": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Manganese"}, "molybdenum": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Molybdenum"}, "niacin": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "pantothenic_acid": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Pantothenic Acid"}, "phosphorus": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Phosphorus"}, "riboflavin": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Riboflavin"}, "selenium": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Selenium"}, "thiamin": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "vitamin_b6": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Vitamin B6"}, "vitamin_b12": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Vitamin B12"}, "vitamin_d": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "vitamin_e": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "vitamin_k": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "zinc": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Zinc"}}, "type": "object", "required": ["fat", "saturated_fat", "polyunsaturated_fat", "monounsaturated_fat", "trans_fat", "cholesterol", "carbohydrates", "fiber", "sugar", "protein", "sodium", "potassium", "vitamin_a", "vitamin_c", "iron", "calcium", "biotin", "caffeine", "chloride", "chromium", "copper", "folate", "iodine", "magnesium", "manganese", "molybdenum", "niacin", "pantothenic_acid", "phosphorus", "riboflavin", "selenium", "thiamin", "vitamin_b6", "vitamin_b12", "vitamin_d", "vitamin_e", "vitamin_k", "zinc"], "title": "Nutrients"}, "OSPlatform": {"type": "string", "enum": ["android", "ios"], "title": "OSPlatform"}, "Organization": {"type": "string", "enum": ["amazon", "apple", "facebook", "fitbit", "garmin", "google", "ll<PERSON>", "netflix", "oura", "best_life", "walmart", "third_party", "unknown"], "title": "Organization", "description": "Currently supported organisations which we can derive data from"}, "Origin": {"type": "string", "enum": ["amazon", "apple", "fitbit", "google", "netflix", "oura", "walmart", "ll<PERSON>", "best_life", "unknown"], "title": "Origin"}, "PatternQueryAPI": {"properties": {"type": {"type": "string", "const": "pattern", "title": "Type"}, "field_names": {"items": {"type": "string"}, "type": "array", "minItems": 1, "title": "Field Names"}, "pattern": {"type": "string", "title": "Pattern"}, "match_type": {"$ref": "#/components/schemas/MatchType", "default": "default"}, "operator": {"$ref": "#/components/schemas/QueryOperator", "default": "or"}}, "type": "object", "required": ["type", "field_names", "pattern"], "title": "PatternQueryAPI"}, "PlaceVisitDetails": {"properties": {"name": {"type": "string", "maxLength": 128, "minLength": 1, "title": "Name"}, "address": {"anyOf": [{"type": "string", "maxLength": 256, "minLength": 1}, {"type": "null"}], "title": "Address"}, "id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Id"}, "confidence": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Confidence"}}, "type": "object", "required": ["name"], "title": "PlaceVisitDetails"}, "PlanAPIOutput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "type": {"type": "string", "const": "plan", "title": "Type"}, "metadata": {"$ref": "#/components/schemas/PlanMetadata"}, "name": {"type": "string", "maxLength": 64, "minLength": 1, "title": "Name"}, "template_id": {"type": "string", "format": "uuid", "title": "Template Id"}, "next_scheduled_at": {"type": "string", "format": "date-time", "title": "Next Scheduled At"}, "first_completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "First Completed At"}, "archived_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Archived At"}, "is_urgent": {"type": "boolean", "title": "<PERSON>"}, "is_confirmation_required": {"type": "boolean", "title": "Is Confirmation Required"}, "is_absolute_schedule": {"type": "boolean", "title": "Is Absolute Schedule"}, "streak": {"$ref": "#/components/schemas/PlanStreak"}, "recurrence": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Recurrence"}, "note": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Note"}, "priority": {"$ref": "#/components/schemas/Priority"}, "prompt": {"type": "string", "minLength": 1, "title": "Prompt"}, "current_completed": {"type": "integer", "minimum": 0.0, "title": "Current Completed"}, "max_completed": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Max Completed"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}}, "type": "object", "required": ["id", "system_properties", "type", "metadata", "name", "template_id", "next_scheduled_at", "first_completed_at", "archived_at", "is_urgent", "is_confirmation_required", "is_absolute_schedule", "streak", "recurrence", "note", "priority", "prompt", "current_completed", "max_completed", "tags"], "title": "PlanAPIOutput"}, "PlanMetadata": {"properties": {"organization": {"$ref": "#/components/schemas/Organization", "default": "ll<PERSON>"}}, "type": "object", "title": "PlanMetadata"}, "PlanStreak": {"properties": {"streak": {"type": "integer", "minimum": 0.0, "title": "Streak"}, "longest_streak": {"type": "integer", "minimum": 0.0, "title": "Longest Streak"}, "total_triggered": {"type": "integer", "minimum": 0.0, "title": "Total Triggered"}}, "type": "object", "required": ["streak", "longest_streak", "total_triggered"], "title": "PlanStreak"}, "PostContentLookupRequestInput": {"properties": {"url": {"type": "string", "maxLength": 2083, "minLength": 1, "format": "uri", "title": "Url", "description": "url to resolve by the lookup"}}, "type": "object", "required": ["url"], "title": "PostContentLookupRequestInput"}, "Priority": {"type": "integer", "enum": [0, 1, 2, 3, 4], "title": "Priority"}, "QueryOperator": {"type": "string", "enum": ["and", "or"], "title": "QueryOperator"}, "RadiusQueryAPI": {"properties": {"type": {"type": "string", "const": "radius", "title": "Type"}, "field_name": {"type": "string", "minLength": 1, "title": "Field Name"}, "radius": {"type": "string", "minLength": 1, "pattern": "^\\d+(m|km|ft|yd|mi)$", "title": "<PERSON><PERSON>"}, "latitude": {"type": "number", "title": "Latitude"}, "longitude": {"type": "number", "title": "Longitude"}}, "type": "object", "required": ["type", "field_name", "radius", "latitude", "longitude"], "title": "RadiusQueryAPI"}, "RangeQueryAPI": {"properties": {"type": {"type": "string", "const": "range", "title": "Type"}, "field_name": {"type": "string", "title": "Field Name"}, "gte": {"anyOf": [{"type": "integer"}, {"type": "number"}, {"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Gte"}, "lte": {"anyOf": [{"type": "integer"}, {"type": "number"}, {"type": "string", "format": "date-time"}, {"type": "null"}], "title": "<PERSON><PERSON>"}}, "type": "object", "required": ["type", "field_name"], "title": "RangeQueryAPI"}, "RecordType": {"type": "string", "enum": ["sleep_record"], "title": "RecordType"}, "RestingHeartRateAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "duration": {"anyOf": [{"type": "integer", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "bpm_avg": {"type": "number", "maximum": 400.0, "minimum": 0.0, "title": "Bpm Avg"}, "bpm_max": {"type": "number", "maximum": 400.0, "minimum": 0.0, "title": "Bpm Max"}, "bpm_min": {"type": "number", "maximum": 400.0, "minimum": 0.0, "title": "Bpm Min"}, "rhr_detail": {"anyOf": [{"items": {"$ref": "#/components/schemas/RestingHeartRateDetail"}, "type": "array"}, {"type": "null"}], "title": "Rhr Detail"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchema"}, "_doc_id": {"type": "string", "format": "uuid", "title": "Doc Id"}, "metadata": {"$ref": "#/components/schemas/MetadataOutput"}, "type": {"type": "string", "const": "RestingHeartRate", "title": "Type"}}, "type": "object", "required": ["timestamp", "bpm_avg", "bpm_max", "bpm_min", "_doc_id", "metadata", "type"], "title": "RestingHeartRateAPIOutput"}, "RestingHeartRateDetail": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "value": {"type": "number", "maximum": 400.0, "minimum": 0.0, "title": "Value"}, "confidence": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Confidence"}}, "type": "object", "required": ["timestamp", "value"], "title": "RestingHeartRateDetail"}, "SearchPlansRequestInput": {"properties": {"sort": {"$ref": "#/components/schemas/SortRequestInput", "default": {"field_name": "system_properties.created_at", "order": "descending"}}, "query": {"anyOf": [{"oneOf": [{"$ref": "#/components/schemas/ValuesQueryAPI"}, {"$ref": "#/components/schemas/PatternQueryAPI"}, {"$ref": "#/components/schemas/RangeQueryAPI"}, {"$ref": "#/components/schemas/ExistsQueryAPI"}, {"$ref": "#/components/schemas/RadiusQueryAPI"}], "discriminator": {"propertyName": "type", "mapping": {"exists": "#/components/schemas/ExistsQueryAPI", "pattern": "#/components/schemas/PatternQueryAPI", "radius": "#/components/schemas/RadiusQueryAPI", "range": "#/components/schemas/RangeQueryAPI", "values": "#/components/schemas/ValuesQueryAPI"}}}, {"$ref": "#/components/schemas/CompoundBooleanQueryAPI-Input"}, {"type": "null"}], "title": "Query"}}, "type": "object", "title": "SearchPlansRequestInput"}, "SearchTemplatesRequestInput": {"properties": {"queries": {"items": {"$ref": "#/components/schemas/TemplateTypedQueryAPI"}, "type": "array", "title": "Queries"}, "sort": {"$ref": "#/components/schemas/SortRequestInput", "default": {"field_name": "system_properties.created_at", "order": "descending"}}}, "type": "object", "title": "SearchTemplatesRequestInput"}, "SearchUseCaseRequestInput": {"properties": {"sort": {"$ref": "#/components/schemas/SortRequestInput", "default": {"field_name": "system_properties.created_at", "order": "descending"}}, "query": {"anyOf": [{"oneOf": [{"$ref": "#/components/schemas/ValuesQueryAPI"}, {"$ref": "#/components/schemas/PatternQueryAPI"}, {"$ref": "#/components/schemas/RangeQueryAPI"}, {"$ref": "#/components/schemas/ExistsQueryAPI"}, {"$ref": "#/components/schemas/RadiusQueryAPI"}], "discriminator": {"propertyName": "type", "mapping": {"exists": "#/components/schemas/ExistsQueryAPI", "pattern": "#/components/schemas/PatternQueryAPI", "radius": "#/components/schemas/RadiusQueryAPI", "range": "#/components/schemas/RangeQueryAPI", "values": "#/components/schemas/ValuesQueryAPI"}}}, {"$ref": "#/components/schemas/CompoundBooleanQueryAPI-Input"}, {"type": "null"}], "title": "Query"}}, "type": "object", "title": "SearchUseCaseRequestInput"}, "Service": {"type": "string", "enum": ["apple_health_kit", "alexa", "voice", "diary", "measure", "search", "google_fit"], "title": "Service", "description": "Supported third party services"}, "SimpleAggregationMethod": {"type": "string", "enum": ["sum", "min", "max", "avg"], "title": "SimpleAggregationMethod"}, "SingleDoseInformation": {"properties": {"amount": {"type": "number", "title": "Amount"}, "amount_unit": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"$ref": "#/components/schemas/WeightUnit"}], "title": "Amount Unit"}, "items_quantity": {"type": "number", "title": "Items Quantity"}}, "type": "object", "required": ["amount", "amount_unit", "items_quantity"], "title": "SingleDoseInformation"}, "SleepAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "sleep_events": {"items": {"$ref": "#/components/schemas/SleepEvent"}, "type": "array", "title": "Sleep Events"}, "end_time": {"type": "string", "format": "date-time", "title": "End Time"}, "duration": {"anyOf": [{"type": "integer", "maximum": 86400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchema"}, "_doc_id": {"type": "string", "format": "uuid", "title": "Doc Id"}, "metadata": {"$ref": "#/components/schemas/MetadataOutput"}, "type": {"type": "string", "const": "Sleep", "title": "Type"}}, "type": "object", "required": ["timestamp", "sleep_events", "end_time", "_doc_id", "metadata", "type"], "title": "SleepAPIOutput"}, "SleepDetail": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "duration": {"anyOf": [{"type": "integer", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "stage": {"$ref": "#/components/schemas/SleepStage"}}, "type": "object", "required": ["timestamp", "stage"], "title": "SleepDetail"}, "SleepEvent": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "sleep_detail": {"anyOf": [{"items": {"$ref": "#/components/schemas/SleepDetail"}, "type": "array"}, {"type": "null"}], "title": "Sleep Detail"}, "sleep_summary": {"$ref": "#/components/schemas/SleepSummary"}, "end_time": {"type": "string", "format": "date-time", "title": "End Time"}, "duration": {"anyOf": [{"type": "integer", "maximum": 86400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}}, "type": "object", "required": ["timestamp", "sleep_summary", "end_time"], "title": "SleepEvent"}, "SleepRecordAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"type": "string", "format": "date-time", "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "type": {"type": "string", "const": "sleep_record", "title": "Type"}, "stage": {"$ref": "#/components/schemas/SleepStage"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "end_time", "id", "system_properties", "type", "stage", "metadata", "duration"], "title": "SleepRecordAPIOutput"}, "SleepStage": {"type": "string", "enum": ["asleep", "awake", "light", "deep", "rem", "restless", "unknown"], "title": "SleepStage"}, "SleepSummary": {"properties": {"efficiency": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "null"}], "title": "Efficiency"}, "is_main_sleep": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Main Sleep"}, "events_count": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Events Count"}, "fall_asleep_seconds": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Fall Asleep Seconds"}, "after_wakeup_seconds": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "After Wakeup Seconds"}, "awake_seconds": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Awake Seconds"}, "asleep_seconds": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "<PERSON>lee<PERSON> Seconds"}, "in_bed_seconds": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "In Bed Seconds"}, "deep_seconds": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Deep Seconds"}, "light_seconds": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Light Seconds"}, "rem_seconds": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "restless_seconds": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Restless Seconds"}}, "type": "object", "title": "SleepSummary"}, "SleepV3APIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "sleep", "title": "Type"}, "category": {"$ref": "#/components/schemas/SleepV3Category"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": -5.0}, {"type": "null"}], "title": "Rating"}, "deep_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Deep Seconds"}, "light_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Light Seconds"}, "rem_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "awake_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Awake Seconds"}, "restless_moments": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Restless Moments"}, "provider_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Provider Score"}, "llif_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "rating", "deep_seconds", "light_seconds", "rem_seconds", "awake_seconds", "restless_moments", "provider_score", "llif_score", "duration"], "title": "SleepV3APIOutput"}, "SleepV3Category": {"type": "string", "enum": ["Sleep"], "title": "SleepV3Category"}, "SleepV3TemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "sleep", "title": "Type"}, "category": {"$ref": "#/components/schemas/SleepV3Category"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "deep_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Deep Seconds"}, "light_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Light Seconds"}, "rem_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "awake_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Awake Seconds"}, "restless_moments": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Restless Moments"}, "provider_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Provider Score"}, "llif_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "rating", "deep_seconds", "light_seconds", "rem_seconds", "awake_seconds", "restless_moments", "provider_score", "llif_score"], "title": "SleepV3TemplatePayload"}, "SortOrder": {"type": "string", "enum": ["ascending", "descending"], "title": "SortOrder"}, "SortRequestInput": {"properties": {"field_name": {"type": "string", "title": "Field Name", "default": "timestamp"}, "order": {"$ref": "#/components/schemas/SortOrder", "default": "descending"}}, "type": "object", "title": "SortRequestInput"}, "SourceOS": {"type": "string", "enum": ["macos", "ios", "android", "ipados", "windows", "linux", "unknown"], "title": "SourceOS"}, "SourceService": {"type": "string", "enum": ["apple_health_kit", "google_fit", "google_health_connect", "amazon_alexa", "takeout", "fitbit", "best_life_app", "usda_food_db", "ai_photo", "ai_text"], "title": "SourceService"}, "StepsAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "duration": {"anyOf": [{"type": "integer", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "steps": {"type": "integer", "minimum": 0.0, "title": "Steps"}, "step_details": {"items": {"$ref": "#/components/schemas/StepsDetail"}, "type": "array", "title": "Step Details"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchema"}, "_doc_id": {"type": "string", "format": "uuid", "title": "Doc Id"}, "metadata": {"$ref": "#/components/schemas/MetadataOutput"}, "type": {"type": "string", "const": "Steps", "title": "Type"}}, "type": "object", "required": ["timestamp", "steps", "step_details", "_doc_id", "metadata", "type"], "title": "StepsAPIOutput"}, "StepsDetail": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "duration": {"anyOf": [{"type": "integer", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "steps": {"type": "integer", "minimum": 0.0, "title": "Steps"}}, "type": "object", "required": ["timestamp", "steps"], "title": "StepsDetail"}, "StrengthAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "strength", "title": "Type"}, "category": {"$ref": "#/components/schemas/StrengthCategory"}, "count": {"type": "integer", "maximum": 1000.0, "minimum": 0.0, "title": "Count"}, "weight": {"anyOf": [{"type": "number", "ge": 0, "le": 500}, {"type": "null"}], "title": "Weight"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "count", "weight", "rating", "duration"], "title": "StrengthAPIOutput"}, "StrengthCategory": {"type": "string", "enum": ["bodyweight", "free_weights", "resistance_bands", "suspension_training", "strength"], "title": "StrengthCategory"}, "StrengthTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "strength", "title": "Type"}, "category": {"$ref": "#/components/schemas/StrengthCategory"}, "count": {"type": "integer", "maximum": 1000.0, "minimum": 0.0, "title": "Count"}, "weight": {"anyOf": [{"type": "number", "ge": 0, "le": 500}, {"type": "null"}], "title": "Weight"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "count", "weight", "rating"], "title": "StrengthTemplatePayload"}, "StressAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "stress", "title": "Type"}, "category": {"$ref": "#/components/schemas/StressCategory"}, "rating": {"type": "integer", "maximum": 10.0, "minimum": -5.0, "title": "Rating"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "rating", "duration"], "title": "StressAPIOutput"}, "StressCategory": {"type": "string", "enum": ["food_consumption", "liquid_consumption", "mental_activity", "physical_activity", "screen_time", "sleep", "social_activity", "stress", "sun_exposure"], "title": "StressCategory"}, "StressTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "stress", "title": "Type"}, "category": {"$ref": "#/components/schemas/StressCategory"}, "rating": {"type": "integer", "maximum": 5.0, "minimum": -5.0, "title": "Rating"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "rating"], "title": "StressTemplatePayload"}, "SuggestCorrelationParametersAPIOutput": {"properties": {"dependent": {"$ref": "#/components/schemas/CorrelationVariableInput-Output"}, "independent": {"$ref": "#/components/schemas/CorrelationVariableInput-Output"}, "temporal_options": {"$ref": "#/components/schemas/CorrelationTemporalOptions"}, "reasoning": {"type": "string", "title": "Reasoning"}}, "type": "object", "required": ["dependent", "independent", "temporal_options", "reasoning"], "title": "SuggestCorrelationParametersAPIOutput"}, "SuggestCorrelationParametersAPIRequestInput": {"properties": {"dependent_query": {"anyOf": [{"$ref": "#/components/schemas/EventTypedQueryAPI-Input"}, {"$ref": "#/components/schemas/EnvironmentTypedQuery"}], "title": "Dependent Query"}, "independent_query": {"anyOf": [{"$ref": "#/components/schemas/EventTypedQueryAPI-Input"}, {"$ref": "#/components/schemas/EnvironmentTypedQuery"}], "title": "Independent Query"}}, "type": "object", "required": ["dependent_query", "independent_query"], "title": "SuggestCorrelationParametersAPIRequestInput"}, "SuggestEventRequestInput": {"properties": {"query": {"type": "string", "title": "Query"}}, "type": "object", "required": ["query"], "title": "SuggestEventRequestInput"}, "SupplementAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "type": {"type": "string", "const": "supplement", "title": "Type"}, "category": {"$ref": "#/components/schemas/SupplementCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "rating", "consumed_amount", "calories", "flavor", "type", "category", "consumed_type", "duration"], "title": "SupplementAPIOutput"}, "SupplementCategory": {"type": "string", "enum": ["sleep", "other", "heart_health", "joint_health", "mental_health", "immune_support", "digestive_health", "daily_essentials", "weight_management", "cognitive_function", "bone_and_muscle_health", "athletic_performance"], "title": "SupplementCategory"}, "SupplementTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "items_per_serving": {"anyOf": [{"type": "number", "ge": 0, "le": 1000}, {"type": "null"}], "title": "Items Per Serving"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "amount_volume": {"anyOf": [{"type": "number", "ge": 0, "le": 1000}, {"type": "null"}], "title": "Amount Volume"}, "unit_volume": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit", "maxLength": 32}, {"type": "null"}]}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "type": {"type": "string", "const": "supplement", "title": "Type"}, "category": {"$ref": "#/components/schemas/SupplementCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}], "title": "Consumed Type"}, "amount_mass": {"anyOf": [{"type": "number", "ge": 0, "le": 1000}, {"type": "null"}], "title": "Amount Mass"}, "unit_mass": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit", "maxLength": 32}, {"type": "null"}]}}, "type": "object", "required": ["name", "duration", "note", "brand", "rating", "items_per_serving", "consumed_amount", "amount_volume", "unit_volume", "calories", "flavor", "nutrients", "type", "category", "consumed_type", "amount_mass", "unit_mass"], "title": "SupplementTemplatePayload"}, "SymptomAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "symptom", "title": "Type"}, "category": {"$ref": "#/components/schemas/SymptomCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "body_parts": {"items": {"$ref": "#/components/schemas/BodyParts"}, "type": "array", "title": "Body Parts"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "type", "category", "rating", "body_parts", "duration"], "title": "SymptomAPIOutput"}, "SymptomCategory": {"type": "string", "enum": ["pain_and_sensations_pain", "pain_and_sensations_burning", "pain_and_sensations_itching", "pain_and_sensations_scratchiness", "pain_and_sensations_tingling", "pain_and_sensations_numbness", "pain_and_sensations_warmth", "pain_and_sensations_tenderness", "pain_and_sensations_crawling", "pain_and_sensations_headache", "pain_and_sensations_genital_itching", "pain_and_sensations_burning_sensation", "pain_and_sensations_migraine", "pain_and_sensations_other", "motor_and_muscle_fine_motor_difficulty", "motor_and_muscle_jerking", "motor_and_muscle_cramping", "motor_and_muscle_muscle_fatigue", "motor_and_muscle_muscle_spasm", "motor_and_muscle_muscle_tension", "motor_and_muscle_paralysis", "motor_and_muscle_shaking", "motor_and_muscle_tremors", "motor_and_muscle_weakness", "motor_and_muscle_clicking", "motor_and_muscle_locking", "motor_and_muscle_soreness", "motor_and_muscle_stiffness", "motor_and_muscle_other", "skin_and_tissue_lump", "skin_and_tissue_rash", "skin_and_tissue_redness", "skin_and_tissue_swelling", "skin_and_tissue_excessive_sweating", "skin_and_tissue_wet", "skin_and_tissue_acne", "skin_and_tissue_blister", "skin_and_tissue_bruising", "skin_and_tissue_hair_growth", "skin_and_tissue_hair_loss", "skin_and_tissue_hives", "skin_and_tissue_pallor", "skin_and_tissue_rough", "skin_and_tissue_yellowing", "skin_and_tissue_discharge", "skin_and_tissue_dry_skin", "skin_and_tissue_bleeding", "skin_and_tissue_night_sweats", "skin_and_tissue_pale_skin", "skin_and_tissue_skin_peeling", "skin_and_tissue_swollen_glands", "skin_and_tissue_other", "systemic_fatigue", "systemic_fever", "systemic_chills", "systemic_loss_of_appetite", "systemic_other", "respiratory_shortness_of_breath", "respiratory_cough", "respiratory_wheezing", "respiratory_runny_nose", "respiratory_stuffy_nose", "respiratory_loss_of_smell", "respiratory_apnea", "respiratory_sneezing", "respiratory_chest_tightness", "respiratory_other", "circulatory_fast_heart_rhythm", "circulatory_slow_heart_rhythm", "circulatory_irregular_heart_rhythm", "circulatory_pounding_heart", "circulatory_palpitations", "circulatory_feeling_faint", "circulatory_cold_hands_feet", "circulatory_other", "digestive_bloating", "digestive_constipation", "digestive_diarrhea", "digestive_flatulence", "digestive_heartburn", "digestive_vomiting", "digestive_abdominal_pain", "digestive_difficulty_swallowing", "digestive_nausea", "digestive_loss_of_taste", "digestive_excessive_thirst", "digestive_dry_mouth", "digestive_bad_breath", "digestive_other", "neurological_seizures", "neurological_fainting", "neurological_dizziness", "neurological_vertigo", "neurological_clumsiness", "neurological_balance_issues", "neurological_other", "vision_blurry_vision", "vision_double_vision", "vision_floaters", "vision_halos", "vision_light_sensitivity", "vision_spots", "vision_watery_eyes", "vision_dryness", "vision_other", "hearing_muffled", "hearing_loss", "hearing_ringing_in_ears", "hearing_sensitivity_to_sound", "hearing_other", "speech_and_voice_slurred_speech", "speech_and_voice_hoarse_voice", "speech_and_voice_loss_of_voice", "speech_and_voice_other", "sleep_vivid_dreaming", "sleep_difficulty_sleeping", "sleep_sleepwalking", "sleep_snoring", "sleep_insomnia", "sleep_excessive_sleepiness", "sleep_other", "urinary_hesitancy", "urinary_intermittent_flow", "urinary_leaking", "urinary_spraying", "urinary_urgency", "urinary_weak_stream", "urinary_discolored_urine", "urinary_painful_urination", "urinary_frequent_urination", "urinary_blood_in_urine", "urinary_incontinence", "urinary_other", "vaginal_bleeding", "vaginal_clear_discharge", "vaginal_cloudy_discharge", "vaginal_sticky_discharge", "vaginal_dryness", "vaginal_irritation", "vaginal_itching", "vaginal_loss_of_sensation", "vaginal_odor", "vaginal_vaginal_discharge", "vaginal_other", "menstrual_cramp", "menstrual_clots", "menstrual_heavy_flow", "menstrual_spotting", "menstrual_ovulation_pain", "menstrual_menstrual_pain", "menstrual_hot_flashes", "menstrual_other", "pregnancy_contractions", "pregnancy_water_breaking", "pregnancy_braxton_hicks_contractions", "pregnancy_bloody_show", "pregnancy_cervical_effacement", "pregnancy_other", "sexual_function_delayed_orgasm", "sexual_function_inability_to_orgasm", "sexual_function_painful_orgasm", "sexual_function_lack_of_arousal", "sexual_function_painful_intercourse", "sexual_function_premature_orgasm", "sexual_function_other", "mental_behavioral_avoidance", "mental_behavioral_craving", "mental_behavioral_loss_of_interest", "mental_behavioral_fidgeting", "mental_behavioral_pacing", "mental_behavioral_anxiety", "mental_behavioral_depression", "mental_behavioral_irritability", "mental_behavioral_other", "mental_cognitive_brain_fog", "mental_cognitive_difficulty_concentrating", "mental_cognitive_confusion", "mental_cognitive_flashbacks", "mental_cognitive_memory_issues", "mental_cognitive_racing_thoughts", "mental_cognitive_ruminating_thoughts", "mental_cognitive_intrusive_thoughts", "mental_cognitive_hallucinations", "mental_cognitive_memory_loss", "mental_cognitive_other"], "title": "SymptomCategory"}, "SymptomTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "symptom", "title": "Type"}, "category": {"$ref": "#/components/schemas/SymptomCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "body_parts": {"items": {"$ref": "#/components/schemas/BodyParts"}, "type": "array", "title": "Body Parts"}}, "type": "object", "required": ["name", "duration", "note", "type", "category", "rating", "body_parts"], "title": "SymptomTemplatePayload"}, "SystemPropertiesSchema": {"properties": {"created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "deleted_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Deleted At"}}, "type": "object", "title": "SystemPropertiesSchema"}, "SystemPropertiesSchemaAPIOutput": {"properties": {"created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "deleted_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Deleted At"}}, "type": "object", "required": ["created_at"], "title": "SystemPropertiesSchemaAPIOutput"}, "TemplateType": {"type": "string", "enum": ["group_template", "event_template"], "title": "TemplateType"}, "TemplateTypedQueryAPI": {"properties": {"types": {"items": {"$ref": "#/components/schemas/TemplateType"}, "type": "array", "minItems": 1, "title": "Types"}, "query": {"anyOf": [{"oneOf": [{"$ref": "#/components/schemas/ValuesQueryAPI"}, {"$ref": "#/components/schemas/PatternQueryAPI"}, {"$ref": "#/components/schemas/RangeQueryAPI"}, {"$ref": "#/components/schemas/ExistsQueryAPI"}, {"$ref": "#/components/schemas/RadiusQueryAPI"}], "discriminator": {"propertyName": "type", "mapping": {"exists": "#/components/schemas/ExistsQueryAPI", "pattern": "#/components/schemas/PatternQueryAPI", "radius": "#/components/schemas/RadiusQueryAPI", "range": "#/components/schemas/RangeQueryAPI", "values": "#/components/schemas/ValuesQueryAPI"}}}, {"$ref": "#/components/schemas/CompoundBooleanQueryAPI-Input"}, {"type": "null"}], "title": "Query"}}, "type": "object", "required": ["types"], "title": "TemplateTypedQueryAPI"}, "TextAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "text", "title": "Type"}, "category": {"$ref": "#/components/schemas/TextCategory"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "title", "type", "category", "duration"], "title": "TextAPIOutput"}, "TextCategory": {"type": "string", "enum": ["text", "book", "blog", "article"], "title": "TextCategory"}, "TextTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "text", "title": "Type"}, "category": {"$ref": "#/components/schemas/TextCategory"}}, "type": "object", "required": ["name", "duration", "note", "title", "url", "rating", "type", "category"], "title": "TextTemplatePayload"}, "TimeInput": {"properties": {"interval": {"type": "string", "minLength": 2, "pattern": "^(1[mhdwMqy]|\\d+(ms|s|m|h|d))$", "title": "Interval", "description": "Allowed intervals are NX where N is an integer and X equals ms, s, m, h, dOR 1X where X equals m, h, d, w, M, q, y"}, "time_gte": {"type": "string", "format": "date-time", "title": "Time Gte"}, "time_lte": {"type": "string", "format": "date-time", "title": "Time Lte"}}, "type": "object", "required": ["interval", "time_gte", "time_lte"], "title": "TimeInput"}, "TrendDetectionAPIOutput": {"properties": {"trend_result": {"$ref": "#/components/schemas/TrendResult", "default": "NO_TREND"}, "coeficient": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Coeficient"}, "intercept": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Intercept"}, "relative_slope": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Relative Slope"}}, "type": "object", "title": "TrendDetectionAPIOutput"}, "TrendDetectionAPIRequestInput": {"properties": {"data_series": {"items": {"type": "number"}, "type": "array", "minItems": 2, "title": "Data Series"}, "relative_slope_threshold": {"type": "number", "title": "Relative Slop<PERSON>hr<PERSON>old", "description": "Min absolute relative slope for a trend (abs(slope / intercept)).", "default": 0.01}, "r2_threshold": {"type": "number", "title": "R2 Threshold", "description": "Min R-squared for significant trend (model fit).", "default": 0.3}}, "type": "object", "required": ["data_series"], "title": "TrendDetectionAPIRequestInput"}, "TrendResult": {"type": "string", "enum": ["UPWARD_TREND", "DOWNWARD_TREND", "NO_TREND"], "title": "TrendResult"}, "UpdateAudioInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "audio", "title": "Type"}, "category": {"$ref": "#/components/schemas/AudioCategory"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "title", "url", "rating", "note", "type", "category"], "title": "UpdateAudioInput"}, "UpdateBloodGlucoseInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "blood_glucose", "title": "Type"}, "category": {"$ref": "#/components/schemas/BloodGlucoseCategory"}, "value": {"type": "number", "maximum": 4000.0, "minimum": 0.0, "title": "Value"}, "specimen_source": {"$ref": "#/components/schemas/BloodGlucoseSpecimenSource"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "value", "specimen_source", "note"], "title": "UpdateBloodGlucoseInput"}, "UpdateBloodPressureInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "blood_pressure", "title": "Type"}, "category": {"$ref": "#/components/schemas/BloodPressureCategory"}, "systolic": {"type": "number", "maximum": 500.0, "minimum": 0.0, "title": "Systolic"}, "diastolic": {"type": "number", "maximum": 500.0, "minimum": 0.0, "title": "Diastolic"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "systolic", "diastolic", "note"], "title": "UpdateBloodPressureInput"}, "UpdateBodyMetricInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "body_metric", "title": "Type"}, "category": {"$ref": "#/components/schemas/BodyMetricCategory"}, "value": {"type": "number", "maximum": 1000000000.0, "minimum": -1000000000.0, "title": "Value"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "value", "note"], "title": "UpdateBodyMetricInput"}, "UpdateCardioInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "cardio", "title": "Type"}, "category": {"$ref": "#/components/schemas/CardioCategory"}, "distance": {"anyOf": [{"type": "number", "ge": 0, "le": 100000}, {"type": "null"}], "title": "Distance"}, "elevation": {"anyOf": [{"type": "number", "ge": -500, "le": 8848}, {"type": "null"}], "title": "Elevation"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "distance", "elevation", "rating", "note"], "title": "UpdateCardioInput"}, "UpdateContentInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "content", "title": "Type"}, "category": {"$ref": "#/components/schemas/ContentCategory"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "title", "url", "rating", "note", "type", "category"], "title": "UpdateContentInput"}, "UpdateCoreEventInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "core_event", "title": "Type"}, "category": {"$ref": "#/components/schemas/CoreEventCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "rating", "note"], "title": "UpdateCoreEventInput"}, "UpdateDrinkInput": {"properties": {"brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "drink", "title": "Type"}, "category": {"$ref": "#/components/schemas/DrinkCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}, {"type": "null"}], "title": "Consumed Type"}}, "type": "object", "required": ["brand", "rating", "note", "consumed_amount", "calories", "flavor", "nutrients", "id", "timestamp", "name", "plan_extension", "tags", "type", "category", "consumed_type"], "title": "UpdateDrinkInput"}, "UpdateEmotionInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "emotion", "title": "Type"}, "category": {"$ref": "#/components/schemas/EmotionCategory"}, "rating": {"type": "integer", "maximum": 10.0, "minimum": 0.0, "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "rating", "note"], "title": "UpdateEmotionInput"}, "UpdateEventAPIRequestInput": {"properties": {"documents": {"items": {"oneOf": [{"$ref": "#/components/schemas/UpdateBloodGlucoseInput"}, {"$ref": "#/components/schemas/UpdateBloodPressureInput"}, {"$ref": "#/components/schemas/UpdateBodyMetricInput"}, {"$ref": "#/components/schemas/UpdateAudioInput"}, {"$ref": "#/components/schemas/UpdateContentInput"}, {"$ref": "#/components/schemas/UpdateImageInput"}, {"$ref": "#/components/schemas/UpdateInteractiveInput"}, {"$ref": "#/components/schemas/UpdateTextInput"}, {"$ref": "#/components/schemas/UpdateVideoInput"}, {"$ref": "#/components/schemas/UpdateCardioInput"}, {"$ref": "#/components/schemas/UpdateExerciseInput"}, {"$ref": "#/components/schemas/UpdateStrengthInput"}, {"$ref": "#/components/schemas/UpdateEmotionInput"}, {"$ref": "#/components/schemas/UpdateStressInput"}, {"$ref": "#/components/schemas/UpdateDrinkInput"}, {"$ref": "#/components/schemas/UpdateFoodInput"}, {"$ref": "#/components/schemas/UpdateSupplementInput"}, {"$ref": "#/components/schemas/UpdateCoreEventInput"}, {"$ref": "#/components/schemas/UpdateSleepV3Input"}, {"$ref": "#/components/schemas/UpdateEventGroupInput"}, {"$ref": "#/components/schemas/UpdateNoteInput"}, {"$ref": "#/components/schemas/UpdateSymptomInput"}, {"$ref": "#/components/schemas/UpdateMedicationInput"}], "discriminator": {"propertyName": "type", "mapping": {"audio": "#/components/schemas/UpdateAudioInput", "blood_glucose": "#/components/schemas/UpdateBloodGlucoseInput", "blood_pressure": "#/components/schemas/UpdateBloodPressureInput", "body_metric": "#/components/schemas/UpdateBodyMetricInput", "cardio": "#/components/schemas/UpdateCardioInput", "content": "#/components/schemas/UpdateContentInput", "core_event": "#/components/schemas/UpdateCoreEventInput", "drink": "#/components/schemas/UpdateDrinkInput", "emotion": "#/components/schemas/UpdateEmotionInput", "event_group": "#/components/schemas/UpdateEventGroupInput", "exercise": "#/components/schemas/UpdateExerciseInput", "food": "#/components/schemas/UpdateFoodInput", "image": "#/components/schemas/UpdateImageInput", "interactive": "#/components/schemas/UpdateInteractiveInput", "medication": "#/components/schemas/UpdateMedicationInput", "note": "#/components/schemas/UpdateNoteInput", "sleep": "#/components/schemas/UpdateSleepV3Input", "strength": "#/components/schemas/UpdateStrengthInput", "stress": "#/components/schemas/UpdateStressInput", "supplement": "#/components/schemas/UpdateSupplementInput", "symptom": "#/components/schemas/UpdateSymptomInput", "text": "#/components/schemas/UpdateTextInput", "video": "#/components/schemas/UpdateVideoInput"}}}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "UpdateEventAPIRequestInput"}, "UpdateEventGroupInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "event_group", "title": "Type"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "note"], "title": "UpdateEventGroupInput"}, "UpdateEventTemplateInputBoundaryItem": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "name": {"type": "string", "minLength": 1, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "document": {"oneOf": [{"$ref": "#/components/schemas/BloodGlucoseTemplatePayload"}, {"$ref": "#/components/schemas/BloodPressureTemplatePayload"}, {"$ref": "#/components/schemas/BodyMetricTemplatePayload"}, {"$ref": "#/components/schemas/AudioTemplatePayload"}, {"$ref": "#/components/schemas/ContentTemplatePayload"}, {"$ref": "#/components/schemas/ImageTemplatePayload"}, {"$ref": "#/components/schemas/InteractiveTemplatePayload"}, {"$ref": "#/components/schemas/TextTemplatePayload"}, {"$ref": "#/components/schemas/VideoTemplatePayload"}, {"$ref": "#/components/schemas/CardioTemplatePayload"}, {"$ref": "#/components/schemas/ExerciseTemplatePayload"}, {"$ref": "#/components/schemas/StrengthTemplatePayload"}, {"$ref": "#/components/schemas/EmotionTemplatePayload"}, {"$ref": "#/components/schemas/StressTemplatePayload"}, {"$ref": "#/components/schemas/DrinkTemplatePayload"}, {"$ref": "#/components/schemas/FoodTemplatePayload"}, {"$ref": "#/components/schemas/SupplementTemplatePayload"}, {"$ref": "#/components/schemas/CoreEventTemplatePayload"}, {"$ref": "#/components/schemas/SleepV3TemplatePayload"}, {"$ref": "#/components/schemas/NoteTemplatePayload"}, {"$ref": "#/components/schemas/SymptomTemplatePayload"}, {"$ref": "#/components/schemas/MedicationTemplatePayload-Input"}], "title": "Document", "discriminator": {"propertyName": "type", "mapping": {"audio": "#/components/schemas/AudioTemplatePayload", "blood_glucose": "#/components/schemas/BloodGlucoseTemplatePayload", "blood_pressure": "#/components/schemas/BloodPressureTemplatePayload", "body_metric": "#/components/schemas/BodyMetricTemplatePayload", "cardio": "#/components/schemas/CardioTemplatePayload", "content": "#/components/schemas/ContentTemplatePayload", "core_event": "#/components/schemas/CoreEventTemplatePayload", "drink": "#/components/schemas/DrinkTemplatePayload", "emotion": "#/components/schemas/EmotionTemplatePayload", "exercise": "#/components/schemas/ExerciseTemplatePayload", "food": "#/components/schemas/FoodTemplatePayload", "image": "#/components/schemas/ImageTemplatePayload", "interactive": "#/components/schemas/InteractiveTemplatePayload", "medication": "#/components/schemas/MedicationTemplatePayload-Input", "note": "#/components/schemas/NoteTemplatePayload", "sleep": "#/components/schemas/SleepV3TemplatePayload", "strength": "#/components/schemas/StrengthTemplatePayload", "stress": "#/components/schemas/StressTemplatePayload", "supplement": "#/components/schemas/SupplementTemplatePayload", "symptom": "#/components/schemas/SymptomTemplatePayload", "text": "#/components/schemas/TextTemplatePayload", "video": "#/components/schemas/VideoTemplatePayload"}}}}, "type": "object", "required": ["id", "name", "tags", "document"], "title": "UpdateEventTemplateInputBoundaryItem"}, "UpdateExerciseInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "exercise", "title": "Type"}, "category": {"$ref": "#/components/schemas/ExerciseCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "rating", "note"], "title": "UpdateExerciseInput"}, "UpdateFoodInput": {"properties": {"brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "food", "title": "Type"}, "category": {"$ref": "#/components/schemas/FoodCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}, {"type": "null"}], "title": "Consumed Type"}}, "type": "object", "required": ["brand", "rating", "note", "consumed_amount", "calories", "flavor", "nutrients", "id", "timestamp", "name", "plan_extension", "tags", "type", "category", "consumed_type"], "title": "UpdateFoodInput"}, "UpdateGroupTemplateInputBoundaryItem": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "name": {"type": "string", "minLength": 1, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "template_ids": {"items": {"type": "string", "format": "uuid"}, "type": "array", "minItems": 1, "title": "Template Ids"}}, "type": "object", "required": ["id", "name", "tags", "template_ids"], "title": "UpdateGroupTemplateInputBoundaryItem"}, "UpdateImageInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "image", "title": "Type"}, "category": {"$ref": "#/components/schemas/ImageCategory"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "title", "url", "rating", "note", "type", "category"], "title": "UpdateImageInput"}, "UpdateInteractiveInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "interactive", "title": "Type"}, "category": {"$ref": "#/components/schemas/InteractiveCategory"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "title", "url", "rating", "note", "type", "category"], "title": "UpdateInteractiveInput"}, "UpdateMedicationInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "medication", "title": "Type"}, "category": {"$ref": "#/components/schemas/MedicationCategory"}, "medication_details": {"$ref": "#/components/schemas/MedicationDetails"}, "single_dose_information": {"$ref": "#/components/schemas/SingleDoseInformation"}, "consumed_amount": {"type": "number", "maximum": 1000.0, "minimum": 0.0, "title": "Consumed Amount"}, "consume_unit": {"anyOf": [{"$ref": "#/components/schemas/VolumeUnit"}, {"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/ConsumeUnit"}], "title": "Consume Unit"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "medication_details", "single_dose_information", "consumed_amount", "consume_unit", "note"], "title": "UpdateMedicationInput"}, "UpdateNoteInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "note", "title": "Type"}, "category": {"$ref": "#/components/schemas/NoteCategory"}, "note": {"type": "string", "maxLength": 8196, "minLength": 1, "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "note"], "title": "UpdateNoteInput"}, "UpdatePlanInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "type": {"type": "string", "const": "plan", "title": "Type"}, "name": {"type": "string", "maxLength": 64, "minLength": 1, "title": "Name"}, "template_id": {"type": "string", "format": "uuid", "title": "Template Id"}, "next_scheduled_at": {"type": "string", "format": "date-time", "title": "Next Scheduled At"}, "streak": {"$ref": "#/components/schemas/PlanStreak"}, "recurrence": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Recurrence", "description": "Expects format of 'DTSTART:'tz_aware_iso8601}\n{OPTS}"}, "is_absolute_schedule": {"type": "boolean", "title": "Is Absolute Schedule"}, "is_urgent": {"type": "boolean", "title": "<PERSON>"}, "is_confirmation_required": {"type": "boolean", "title": "Is Confirmation Required"}, "priority": {"$ref": "#/components/schemas/Priority", "default": 0}, "prompt": {"type": "string", "minLength": 1, "title": "Prompt"}, "current_completed": {"type": "integer", "minimum": 0.0, "title": "Current Completed"}, "note": {"anyOf": [{"type": "string", "minLength": 1}, {"type": "null"}], "title": "Note"}, "max_completed": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Max Completed"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}}, "type": "object", "required": ["id", "type", "name", "template_id", "next_scheduled_at", "streak", "recurrence", "is_absolute_schedule", "is_urgent", "is_confirmation_required", "prompt", "current_completed", "note", "max_completed", "tags"], "title": "UpdatePlanInput"}, "UpdatePlansAPIRequestInput": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/UpdatePlanInput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "UpdatePlansAPIRequestInput"}, "UpdateSleepV3Input": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "sleep", "title": "Type"}, "category": {"$ref": "#/components/schemas/SleepV3Category"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}, "deep_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Deep Seconds"}, "light_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Light Seconds"}, "rem_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "awake_seconds": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Awake Seconds"}, "restless_moments": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Restless Moments"}, "provider_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Provider Score"}, "llif_score": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "rating", "note", "deep_seconds", "light_seconds", "rem_seconds", "awake_seconds", "restless_moments", "provider_score", "llif_score"], "title": "UpdateSleepV3Input"}, "UpdateStrengthInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "strength", "title": "Type"}, "category": {"$ref": "#/components/schemas/StrengthCategory"}, "count": {"type": "integer", "maximum": 1000.0, "minimum": 0.0, "title": "Count"}, "weight": {"anyOf": [{"type": "number", "ge": 0, "le": 500}, {"type": "null"}], "title": "Weight"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "count", "weight", "rating", "note"], "title": "UpdateStrengthInput"}, "UpdateStressInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "stress", "title": "Type"}, "category": {"$ref": "#/components/schemas/StressCategory"}, "rating": {"type": "integer", "maximum": 5.0, "minimum": -5.0, "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196, "minLength": 1}, {"type": "null"}], "title": "Note"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "rating", "note"], "title": "UpdateStressInput"}, "UpdateSupplementInput": {"properties": {"brand": {"anyOf": [{"type": "string", "maxLength": 64}, {"type": "null"}], "title": "Brand"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "consumed_amount": {"type": "number", "maximum": 25000.0, "minimum": 0.0, "title": "Consumed Amount"}, "calories": {"anyOf": [{"type": "number", "ge": 0, "le": 10000}, {"type": "null"}], "title": "Calories"}, "flavor": {"anyOf": [{"type": "string", "maxLength": 128}, {"type": "null"}], "title": "Flavor"}, "nutrients": {"anyOf": [{"$ref": "#/components/schemas/Nutrients"}, {"type": "null"}]}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "supplement", "title": "Type"}, "category": {"$ref": "#/components/schemas/SupplementCategory"}, "consumed_type": {"anyOf": [{"$ref": "#/components/schemas/WeightUnit"}, {"$ref": "#/components/schemas/VolumeUnit"}, {"type": "string", "enum": ["item", "serving"]}, {"type": "null"}], "title": "Consumed Type"}}, "type": "object", "required": ["brand", "rating", "note", "consumed_amount", "calories", "flavor", "nutrients", "id", "timestamp", "name", "plan_extension", "tags", "type", "category", "consumed_type"], "title": "UpdateSupplementInput"}, "UpdateSymptomInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "type": {"type": "string", "const": "symptom", "title": "Type"}, "category": {"$ref": "#/components/schemas/SymptomCategory"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "body_parts": {"items": {"$ref": "#/components/schemas/BodyParts"}, "type": "array", "title": "Body Parts"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "type", "category", "rating", "note", "body_parts"], "title": "UpdateSymptomInput"}, "UpdateTemplateAPIRequestInput": {"properties": {"documents": {"items": {"anyOf": [{"$ref": "#/components/schemas/UpdateEventTemplateInputBoundaryItem"}, {"$ref": "#/components/schemas/UpdateGroupTemplateInputBoundaryItem"}]}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "UpdateTemplateAPIRequestInput"}, "UpdateTextInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "text", "title": "Type"}, "category": {"$ref": "#/components/schemas/TextCategory"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "title", "url", "rating", "note", "type", "category"], "title": "UpdateTextInput"}, "UpdateUseCaseAPIRequestInput": {"properties": {"documents": {"items": {"$ref": "#/components/schemas/UpdateUseCaseInput"}, "type": "array", "minItems": 1, "title": "Documents"}}, "type": "object", "required": ["documents"], "title": "UpdateUseCaseAPIRequestInput"}, "UpdateUseCaseInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "name": {"type": "string", "minLength": 1, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}}, "type": "object", "required": ["id", "name"], "title": "UpdateUseCaseInput"}, "UpdateVideoInput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "name": {"type": "string", "maxLength": 256, "minLength": 1, "title": "Name"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Note"}, "type": {"type": "string", "const": "video", "title": "Type"}, "category": {"$ref": "#/components/schemas/VideoCategory"}}, "type": "object", "required": ["id", "timestamp", "name", "plan_extension", "tags", "title", "url", "rating", "note", "type", "category"], "title": "UpdateVideoInput"}, "UseCaseAPIOutput": {"properties": {"id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "type": {"type": "string", "const": "use_case", "title": "Type"}, "name": {"type": "string", "title": "Name"}, "archived_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Archived At"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}}, "type": "object", "required": ["id", "system_properties", "type", "name", "archived_at", "tags"], "title": "UseCaseAPIOutput"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "ValuesQueryAPI": {"properties": {"type": {"type": "string", "const": "values", "title": "Type"}, "field_name": {"type": "string", "minLength": 1, "title": "Field Name"}, "values": {"items": {"type": "string"}, "type": "array", "minItems": 1, "title": "Values"}}, "type": "object", "required": ["type", "field_name", "values"], "title": "ValuesQueryAPI"}, "VideoAPIOutput": {"properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "system_properties": {"$ref": "#/components/schemas/SystemPropertiesSchemaAPIOutput"}, "name": {"type": "string", "maxLength": 256, "title": "Name"}, "submission_id": {"type": "string", "format": "uuid", "title": "Submission Id"}, "template_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Template Id"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "group_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Group Id"}, "metadata": {"$ref": "#/components/schemas/EventMetadataAPIOutput"}, "asset_references": {"anyOf": [{"items": {"$ref": "#/components/schemas/AssetReference"}, "type": "array", "minItems": 1}, {"type": "null"}], "title": "Asset References"}, "plan_extension": {"anyOf": [{"$ref": "#/components/schemas/EventPlanExtension"}, {"type": "null"}]}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "video", "title": "Type"}, "category": {"$ref": "#/components/schemas/VideoCategory"}, "duration": {"type": "number", "title": "Duration", "readOnly": true}}, "type": "object", "required": ["timestamp", "id", "system_properties", "name", "submission_id", "template_id", "tags", "group_id", "metadata", "asset_references", "plan_extension", "title", "type", "category", "duration"], "title": "VideoAPIOutput"}, "VideoCategory": {"type": "string", "enum": ["video", "movie", "tv_show", "livestream", "podcast", "music_video"], "title": "VideoCategory"}, "VideoTemplatePayload": {"properties": {"name": {"type": "string", "maxLength": 256, "title": "Name"}, "tags": {"items": {"type": "string"}, "type": "array", "maxItems": 64, "title": "Tags"}, "duration": {"anyOf": [{"type": "number", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}, "note": {"anyOf": [{"type": "string", "maxLength": 8196}, {"type": "null"}], "title": "Note"}, "title": {"anyOf": [{"type": "string", "maxLength": 256}, {"type": "null"}], "title": "Title"}, "url": {"anyOf": [{"type": "string", "maxLength": 2056}, {"type": "null"}], "title": "Url"}, "rating": {"anyOf": [{"type": "integer", "maximum": 10.0, "minimum": 0.0}, {"type": "null"}], "title": "Rating"}, "type": {"type": "string", "const": "video", "title": "Type"}, "category": {"$ref": "#/components/schemas/VideoCategory"}}, "type": "object", "required": ["name", "duration", "note", "title", "url", "rating", "type", "category"], "title": "VideoTemplatePayload"}, "VolumeUnit": {"type": "string", "enum": ["ml", "l", "tsp", "tbsp", "fl_oz", "cup", "pt", "qt", "gal"], "title": "VolumeUnit"}, "WaypointDetails": {"properties": {"coordinates": {"anyOf": [{"$ref": "#/components/schemas/CoordinatesModel"}, {"type": "string"}], "title": "Coordinates"}, "altitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Altitude"}, "velocity": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Velocity"}, "hor_acc": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Hor Acc"}, "ver_acc": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Ver Acc"}, "heading": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Heading"}, "source": {"anyOf": [{"$ref": "#/components/schemas/LocationSource"}, {"type": "null"}]}, "platform_type": {"anyOf": [{"$ref": "#/components/schemas/OSPlatform"}, {"type": "null"}]}, "timestamp": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Timestamp"}, "end_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "End Time"}, "duration": {"anyOf": [{"type": "integer", "maximum": 31622400.0, "minimum": 0.0}, {"type": "null"}], "title": "Duration"}}, "type": "object", "required": ["coordinates"], "title": "WaypointDetails"}, "WeightUnit": {"type": "string", "enum": ["mcg", "mg", "g", "kg", "oz", "lb"], "title": "WeightUnit"}}, "securitySchemes": {"CustomHTTPBearer": {"type": "http", "scheme": "bearer"}}}}